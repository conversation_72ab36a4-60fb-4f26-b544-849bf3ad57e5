#!/usr/bin/env python3
"""
Complete Task 2 Experiment Suite
Runs all experiments for batch processing performance analysis
"""

import subprocess
import time
import os
import sys

def run_experiment(name, args, description):
    """Run a single experiment."""
    print(f"\n{'='*60}")
    print(f"🧪 Running: {name}")
    print(f"📝 Description: {description}")
    print(f"⚙️  Command: python3 simple_batch_generator.py {args}")
    print(f"{'='*60}")

    start_time = time.time()

    try:
        # Change to frontend directory and run the command
        cmd = f"cd lmcache-vllm-extended/frontend && python3 simple_batch_generator.py {args}"
        result = subprocess.run(
            cmd,
            shell=True,
            capture_output=True,
            text=True,
            timeout=300  # 5 minute timeout
        )

        end_time = time.time()
        duration = end_time - start_time

        if result.returncode == 0:
            print(f"✅ SUCCESS ({duration:.1f}s)")
            # Print last few lines of output for summary
            lines = result.stdout.strip().split('\n')
            for line in lines[-3:]:
                if line.strip():
                    print(f"   {line}")
        else:
            print(f"❌ FAILED ({duration:.1f}s)")
            print(f"Error: {result.stderr}")
            return False

    except subprocess.TimeoutExpired:
        print(f"⏰ TIMEOUT (5 minutes)")
        return False
    except Exception as e:
        print(f"💥 EXCEPTION: {e}")
        return False

    return True

def main():
    """Run complete Task 2 experiment suite."""

    print("🎯 Starting Complete Task 2 Experiment Suite")
    print("📊 This will test batch processing performance with different configurations")

    # Check if server is running
    print("\n🔍 Checking server status...")
    try:
        result = subprocess.run("curl -s http://localhost:8000/v1/models", shell=True, capture_output=True)
        if result.returncode != 0:
            print("❌ Server not responding. Please start the vLLM server first.")
            sys.exit(1)
        print("✅ Server is running")
    except:
        print("❌ Cannot check server status. Please ensure vLLM server is running.")
        sys.exit(1)

    # Create results directory
    results_dir = "task2_complete_results"
    os.makedirs(results_dir, exist_ok=True)

    # Define all experiments
    experiments = [
        # 1. Scheduler Effectiveness (different repeat ratios)
        ("scheduler_effectiveness_no_repeat",
         f"--ip localhost --port 8000 --num_batches 5 --batch_size 10 --repeat_ratio 0.0 --output {results_dir}/scheduler_no_repeat.json --use_v1_api",
         "No context reuse - baseline performance"),

        ("scheduler_effectiveness_low_repeat",
         f"--ip localhost --port 8000 --num_batches 5 --batch_size 10 --repeat_ratio 0.2 --output {results_dir}/scheduler_low_repeat.json --use_v1_api",
         "20% context reuse"),

        ("scheduler_effectiveness_medium_repeat",
         f"--ip localhost --port 8000 --num_batches 5 --batch_size 10 --repeat_ratio 0.4 --output {results_dir}/scheduler_medium_repeat.json --use_v1_api",
         "40% context reuse"),

        ("scheduler_effectiveness_high_repeat",
         f"--ip localhost --port 8000 --num_batches 5 --batch_size 10 --repeat_ratio 0.6 --output {results_dir}/scheduler_high_repeat.json --use_v1_api",
         "60% context reuse"),

        ("scheduler_effectiveness_very_high_repeat",
         f"--ip localhost --port 8000 --num_batches 5 --batch_size 10 --repeat_ratio 0.8 --output {results_dir}/scheduler_very_high_repeat.json --use_v1_api",
         "80% context reuse"),

        # 2. Batch Size Impact
        ("batch_size_small",
         f"--ip localhost --port 8000 --num_batches 5 --batch_size 5 --repeat_ratio 0.4 --output {results_dir}/batch_size_5.json --use_v1_api",
         "Small batch size (5 requests)"),

        ("batch_size_medium",
         f"--ip localhost --port 8000 --num_batches 5 --batch_size 10 --repeat_ratio 0.4 --output {results_dir}/batch_size_10.json --use_v1_api",
         "Medium batch size (10 requests)"),

        ("batch_size_large",
         f"--ip localhost --port 8000 --num_batches 5 --batch_size 20 --repeat_ratio 0.4 --output {results_dir}/batch_size_20.json --use_v1_api",
         "Large batch size (20 requests)"),

        ("batch_size_very_large",
         f"--ip localhost --port 8000 --num_batches 3 --batch_size 30 --repeat_ratio 0.4 --output {results_dir}/batch_size_30.json --use_v1_api",
         "Very large batch size (30 requests)"),

        # 3. Request Diversity Impact
        ("diversity_very_low",
         f"--ip localhost --port 8000 --num_batches 5 --batch_size 10 --repeat_ratio 0.4 --diversity 0.2 --output {results_dir}/diversity_0.2.json --use_v1_api",
         "Very low diversity (20% unique contexts)"),

        ("diversity_low",
         f"--ip localhost --port 8000 --num_batches 5 --batch_size 10 --repeat_ratio 0.4 --diversity 0.4 --output {results_dir}/diversity_0.4.json --use_v1_api",
         "Low diversity (40% unique contexts)"),

        ("diversity_medium",
         f"--ip localhost --port 8000 --num_batches 5 --batch_size 10 --repeat_ratio 0.4 --diversity 0.6 --output {results_dir}/diversity_0.6.json --use_v1_api",
         "Medium diversity (60% unique contexts)"),

        ("diversity_high",
         f"--ip localhost --port 8000 --num_batches 5 --batch_size 10 --repeat_ratio 0.4 --diversity 0.8 --output {results_dir}/diversity_0.8.json --use_v1_api",
         "High diversity (80% unique contexts)"),

        ("diversity_very_high",
         f"--ip localhost --port 8000 --num_batches 5 --batch_size 10 --repeat_ratio 0.4 --diversity 1.0 --output {results_dir}/diversity_1.0.json --use_v1_api",
         "Very high diversity (100% unique contexts)"),

        # 4. Processing Mode Comparison
        ("processing_sequential",
         f"--ip localhost --port 8000 --num_batches 3 --batch_size 10 --repeat_ratio 0.4 --processing_mode sequential --output {results_dir}/processing_sequential.json --use_v1_api",
         "Sequential processing mode"),

        ("processing_parallel_3",
         f"--ip localhost --port 8000 --num_batches 3 --batch_size 10 --repeat_ratio 0.4 --processing_mode parallel --max_concurrent 3 --output {results_dir}/processing_parallel_3.json --use_v1_api",
         "Parallel processing (3 concurrent)"),

        ("processing_parallel_5",
         f"--ip localhost --port 8000 --num_batches 3 --batch_size 10 --repeat_ratio 0.4 --processing_mode parallel --max_concurrent 5 --output {results_dir}/processing_parallel_5.json --use_v1_api",
         "Parallel processing (5 concurrent)"),

        ("processing_parallel_10",
         f"--ip localhost --port 8000 --num_batches 3 --batch_size 10 --repeat_ratio 0.4 --processing_mode parallel --max_concurrent 10 --output {results_dir}/processing_parallel_10.json --use_v1_api",
         "Parallel processing (10 concurrent)"),
    ]

    # Run all experiments
    successful_experiments = 0
    total_experiments = len(experiments)

    start_time = time.time()

    for name, args, description in experiments:
        success = run_experiment(name, args, description)
        if success:
            successful_experiments += 1

        # Small delay between experiments
        time.sleep(2)

    end_time = time.time()
    total_duration = end_time - start_time

    # Summary
    print(f"\n{'='*60}")
    print(f"📊 EXPERIMENT SUITE COMPLETE")
    print(f"{'='*60}")
    print(f"✅ Successful: {successful_experiments}/{total_experiments}")
    print(f"⏱️  Total time: {total_duration/60:.1f} minutes")
    print(f"📁 Results saved in: {results_dir}/")

    if successful_experiments == total_experiments:
        print(f"\n🎉 All experiments completed successfully!")
        print(f"🔍 Next step: Run analysis with:")
        print(f"   python3 batch_analyzer.py --input_files {results_dir}/*.json --output_dir task2_analysis")
    else:
        print(f"\n⚠️  Some experiments failed. Check the output above for details.")

    return successful_experiments == total_experiments

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)

#!/usr/bin/env python3
"""
Key Task 2 Experiments - Simplified version
Runs the most important experiments for batch processing analysis
"""

import subprocess
import time
import os

def run_experiment(name, args, description):
    """Run a single experiment."""
    print(f"\n{'='*50}")
    print(f"🧪 {name}")
    print(f"📝 {description}")
    print(f"{'='*50}")
    
    start_time = time.time()
    
    try:
        result = subprocess.run(
            f"python3 simple_batch_generator.py {args}",
            shell=True,
            capture_output=True,
            text=True,
            timeout=180  # 3 minute timeout
        )
        
        end_time = time.time()
        duration = end_time - start_time
        
        if result.returncode == 0:
            print(f"✅ SUCCESS ({duration:.1f}s)")
            # Print summary lines
            lines = result.stdout.strip().split('\n')
            for line in lines[-3:]:
                if line.strip():
                    print(f"   {line}")
            return True
        else:
            print(f"❌ FAILED ({duration:.1f}s)")
            print(f"Error: {result.stderr}")
            return False
            
    except subprocess.TimeoutExpired:
        print(f"⏰ TIMEOUT")
        return False
    except Exception as e:
        print(f"💥 EXCEPTION: {e}")
        return False

def main():
    """Run key Task 2 experiments."""
    
    print("🎯 Running Key Task 2 Experiments")
    print("📊 Testing batch processing performance")
    
    # Create results directory
    results_dir = "task2_key_results"
    os.makedirs(results_dir, exist_ok=True)
    
    # Define key experiments
    experiments = [
        # 1. Scheduler Effectiveness
        ("No Context Reuse", 
         f"--ip localhost --port 8000 --num_batches 3 --batch_size 8 --repeat_ratio 0.0 --output {results_dir}/no_reuse.json --use_v1_api",
         "Baseline - no context reuse"),
        
        ("Medium Context Reuse", 
         f"--ip localhost --port 8000 --num_batches 3 --batch_size 8 --repeat_ratio 0.4 --output {results_dir}/medium_reuse.json --use_v1_api",
         "40% context reuse"),
        
        ("High Context Reuse", 
         f"--ip localhost --port 8000 --num_batches 3 --batch_size 8 --repeat_ratio 0.8 --output {results_dir}/high_reuse.json --use_v1_api",
         "80% context reuse"),
        
        # 2. Batch Size Impact
        ("Small Batches", 
         f"--ip localhost --port 8000 --num_batches 4 --batch_size 5 --repeat_ratio 0.4 --output {results_dir}/batch_5.json --use_v1_api",
         "Small batch size"),
        
        ("Large Batches", 
         f"--ip localhost --port 8000 --num_batches 2 --batch_size 15 --repeat_ratio 0.4 --output {results_dir}/batch_15.json --use_v1_api",
         "Large batch size"),
        
        # 3. Request Diversity
        ("Low Diversity", 
         f"--ip localhost --port 8000 --num_batches 3 --batch_size 8 --repeat_ratio 0.4 --diversity 0.3 --output {results_dir}/low_diversity.json --use_v1_api",
         "Low diversity (30% unique contexts)"),
        
        ("High Diversity", 
         f"--ip localhost --port 8000 --num_batches 3 --batch_size 8 --repeat_ratio 0.4 --diversity 1.0 --output {results_dir}/high_diversity.json --use_v1_api",
         "High diversity (100% unique contexts)"),
        
        # 4. Processing Modes
        ("Sequential Processing", 
         f"--ip localhost --port 8000 --num_batches 2 --batch_size 8 --repeat_ratio 0.4 --processing_mode sequential --output {results_dir}/sequential.json --use_v1_api",
         "Sequential request processing"),
        
        ("Parallel Processing", 
         f"--ip localhost --port 8000 --num_batches 2 --batch_size 8 --repeat_ratio 0.4 --processing_mode parallel --max_concurrent 4 --output {results_dir}/parallel.json --use_v1_api",
         "Parallel request processing"),
    ]
    
    # Run experiments
    successful = 0
    total = len(experiments)
    
    start_time = time.time()
    
    for name, args, description in experiments:
        success = run_experiment(name, args, description)
        if success:
            successful += 1
        
        # Small delay between experiments
        time.sleep(1)
    
    end_time = time.time()
    total_duration = end_time - start_time
    
    # Summary
    print(f"\n{'='*50}")
    print(f"📊 EXPERIMENTS COMPLETE")
    print(f"{'='*50}")
    print(f"✅ Successful: {successful}/{total}")
    print(f"⏱️  Total time: {total_duration/60:.1f} minutes")
    print(f"📁 Results in: {results_dir}/")
    
    if successful == total:
        print(f"\n🎉 All experiments completed!")
        print(f"🔍 Run analysis:")
        print(f"   python3 batch_analyzer.py --input_files {results_dir}/*.json --output_dir task2_analysis")
    else:
        print(f"\n⚠️  {total-successful} experiments failed")
    
    return successful == total

if __name__ == "__main__":
    success = main()
    exit(0 if success else 1)

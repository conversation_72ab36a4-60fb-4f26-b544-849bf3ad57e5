from transformers import AutoModel, AutoTokenizer
import torch
import numpy as np
import pandas as pd

def get_approprite_context(query: str) -> int:
    """
    Match the query with the most similar context chunk.
    """
    MODEL_NAME = "Qwen/Qwen2.5-1.5B"

    def get_embedding(text: str) -> np.ndarray:
        inputs = tokenizer(text, return_tensors="pt", truncation=True, max_length=512).to(device)
        with torch.no_grad():
            outputs = model(**inputs, output_hidden_states=True)
            hidden_states = outputs.hidden_states[-1]  # last layer
            sentence_embedding = hidden_states.mean(dim=1).squeeze().cpu().numpy()  # [hidden_size]
        return sentence_embedding

    device = torch.device("cpu")
    tokenizer = AutoTokenizer.from_pretrained(MODEL_NAME)
    model = AutoModel.from_pretrained(MODEL_NAME).to(device)

    query_embedding = get_embedding(query)

    df = pd.read_parquet("embedded_chunks.parquet")
    embedded_chunks = {idx: row.to_numpy() for idx, row in df.iterrows()}

    similarities = {}

    for key, embedding in embedded_chunks.items():  # Convert numpy array to list for dot product
        similarity = np.dot(query_embedding, embedding) / (np.linalg.norm(query_embedding) * np.linalg.norm(embedding))
        similarities[key] = similarity

    # Sort the similarities in descending order
    sorted_similarities = sorted(similarities.items(), key=lambda item: item[1], reverse=True)

    # return the top 1 most similar chunk name
    top_chunk_name = sorted_similarities[0][0]

    # output the top 5 chunk name.
    top_5_chunks = sorted_similarities[:5]
    top_5_chunk_names = [chunk[0] for chunk in top_5_chunks]
    print("Top 5 chunks:", ", ".join(top_5_chunk_names))

    # output the top chunk name as txt. 
    with open("top_chunk.txt", "w") as f:
        f.write(top_chunk_name)

    # output the top chunk name
    print(f"Top chunk name: {top_chunk_name}")

    # output the user query as txt.
    with open("user_query.txt", "w") as f:
        f.write(query)


    return 0


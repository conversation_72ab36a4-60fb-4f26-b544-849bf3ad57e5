from transformers import AutoModel, AutoTokenizer
import torch
import numpy as np

device = torch.device("cuda" if torch.cuda.is_available() else "cpu")
MODEL_NAME = "Qwen/Qwen2.5-1.5B"

tokenizer = AutoTokenizer.from_pretrained(MODEL_NAME)
model = AutoModel.from_pretrained(MODEL_NAME).to(device)

def get_embedding(text: str) -> np.ndarray:
    inputs = tokenizer(text, return_tensors="pt", truncation=True, max_length=512).to(device)
    with torch.no_grad():
        outputs = model(**inputs, output_hidden_states=True)
        hidden_states = outputs.hidden_states[-1]  # last layer
        sentence_embedding = hidden_states.mean(dim=1).squeeze().cpu().numpy()  # [hidden_size]
    return sentence_embedding

mytext1 = "Apple."
mytext2 = "Banana."
mytext3 = "I'm Running."
embedding1 = get_embedding(mytext1)
embedding2 = get_embedding(mytext2)
embedding3 = get_embedding(mytext3)

print(embedding1.shape)
print(embedding2.shape)
print(embedding3.shape)

myquery = "I'm Running. Right?"
embedding_query = get_embedding(myquery)

print(embedding_query.shape)

similarity1 = np.dot(embedding1, embedding_query) / (np.linalg.norm(embedding1) * np.linalg.norm(embedding_query))
similarity2 = np.dot(embedding2, embedding_query) / (np.linalg.norm(embedding2) * np.linalg.norm(embedding_query))
similarity3 = np.dot(embedding3, embedding_query) / (np.linalg.norm(embedding3) * np.linalg.norm(embedding_query))
print(f"Similarity between '{mytext1}' and query '{myquery}': {similarity1:.4f}")
print(f"Similarity between '{mytext2}' and query '{myquery}': {similarity2:.4f}")
print(f"Similarity between '{mytext3}' and query '{myquery}': {similarity3:.4f}")
 

import time
import os, sys
import numpy as np
import pandas as pd
import streamlit as st
import chat_session
from typing import List, Dict
from transformers import AutoTokenizer

# Change the following variables as needed
MODEL_NAME = "Qwen/Qwen2.5-1.5B-Instruct"
IP1 = "************"
PORT1 = 8000

@st.cache_resource
def get_tokenizer():
    global MODEL_NAME
    tokenizer = AutoTokenizer.from_pretrained(MODEL_NAME)
    return tokenizer


tokenizer = get_tokenizer()


@st.cache_data
def read_chunks(file_folder) -> Dict[str, str]:
    """
    Read all the txt files in the folder and return the filenames
    """
    filenames = os.listdir(file_folder)
    ret = {}
    for filename in filenames:
        if not filename.endswith("txt"):
            continue
        key = filename.removesuffix(".txt")
        with open(os.path.join(file_folder, filename), "r") as fin:
            value = fin.read()
        ret[key] = value

    return ret

chunks = read_chunks("data/")
system_prompt = " You are a helpful assistant. I will now give you a document and please answer my question afterwards based on the content in document. "
prompt = " Please summarize the main contribution of the paper below in 3-5 sentences. "

session = chat_session.ChatSession(IP1,PORT1)

for var in chunks:
    selected_chunks = [var] 
    contexts = [chunks[key] for key in selected_chunks]
    session.final_context = ""  # 清空上次的上下文
    session.set_context([system_prompt] + contexts)
    num_tokens = tokenizer.encode(session.get_context())
    st.write(f"Number of tokens: {len(num_tokens)}")


    placeholder = st.empty()
    response = ""
    for chunk in session.chat(prompt):
        print("Got chunk:", repr(chunk))  # 加这句
        response += chunk
        placeholder.markdown(response)
        time.sleep(0.05)
    



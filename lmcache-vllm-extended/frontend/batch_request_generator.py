#!/usr/bin/env python3
import os
import time
import random
import json
import numpy as np
import asyncio
import aiohttp
from collections import defaultdict
from typing import List, Dict, Any, <PERSON><PERSON>
from transformers import AutoTokenizer
from dataclasses import dataclass, asdict


@dataclass
class Request:
    id: str
    context: str
    question: str
    sequence_length: int
    timestamp: float
    context_hash: str = ""

    def __post_init__(self):
        # Create a hash for the context to group similar requests
        self.context_hash = str(hash(self.context))


@dataclass
class Batch:
    id: str
    requests: List[Request]
    timestamp: float
    size: int

    def __post_init__(self):
        self.size = len(self.requests)


class BatchRequestGenerator:

    def __init__(self, model_name="Qwen/Qwen2.5-1.5B-Instruct"):
        self.tokenizer = AutoTokenizer.from_pretrained(model_name)
        self.request_counter = 0
        self.batch_counter = 0

    def load_contexts(self, data_dir="data"):
        contexts = []
        for filename in os.listdir(data_dir):
            if filename.endswith(".txt"):
                with open(os.path.join(data_dir, filename), "r", encoding="utf-8") as f:
                    contexts.append(f.read())
        return contexts

    def measure_sequence_length(self, context, question):
        combined_text = context + " " + question
        tokens = self.tokenizer.encode(combined_text)
        return len(tokens)

    def create_request(self, context: str, question: str) -> Request:
        self.request_counter += 1
        seq_len = self.measure_sequence_length(context, question)

        return Request(
            id=f"req_{self.request_counter}",
            context=context,
            question=question,
            sequence_length=seq_len,
            timestamp=time.time()
        )

    def generate_batch(
        self,
        contexts: List[str],
        questions: List[str],
        batch_size: int,
        repeat_ratio: float = 0.1,
        diversity_level: float = 1.0
        ) -> Batch:

        if diversity_level < 1.0:
            num_contexts = max(1, int(len(contexts) * diversity_level))
            context_pool = random.sample(contexts, num_contexts)
        else:
            context_pool = contexts
        
        repeat_num = int(batch_size * repeat_ratio)
        new_num = batch_size - repeat_num

        used_contexts = random.sample(context_pool, new_num)
        requests = []

        for context in used_contexts:
            question = random.choice(questions)
            request = self.create_request(context, question)
            requests.append(request)

        for _ in range(repeat_num):
            context = random.choice(used_contexts)
            question = random.choice(questions)
            request = self.create_request(context, question)
            requests.append(request)

        random.shuffle(requests)

        self.batch_counter += 1
        return Batch(
            id=f"batch_{self.batch_counter}",
            requests=requests,
            timestamp=time.time(),
            size=len(requests)
        )


    def generate_multiple_batches(
        self,
        contexts: List[str],
        questions: List[str],
        num_batches: int,
        batch_size: int,
        repeat_ratio: float = 0.1,
        diversity_level: float = 1.0
    ) -> List[Batch]:
        """Generate multiple batches of requests."""
        batches = []

        for _ in range(num_batches):
            batch = self.generate_batch(
                contexts=contexts,
                questions=questions,
                batch_size=batch_size,
                repeat_ratio=repeat_ratio,
                diversity_level=diversity_level
            )
            batches.append(batch)

        return batches


class RequestScheduler:

    def __init__(self):
        self.processed_contexts = set()
        self.context_groups = defaultdict(list)

    def schedule_batch(self, batch: Batch) -> List[Request]:
        """
        Schedule requests in a batch to optimize performance.

        Strategy:
        1. Group requests by context
        2. Process groups with cached contexts first
        3. Within each group, process requests sequentially
        4. Order groups by context size (smaller first for faster initial processing)
        """
        # Group requests by context hash
        context_groups = defaultdict(list)
        for request in batch.requests:
            context_groups[request.context_hash].append(request)

        scheduled_requests = []

        """Print the context distribution of the current batch(debug for test2)"""
        
        print(f"\n[Scheduler] Batch {batch.id} context grouping summary:")
        total_requests = len(batch.requests)
        for context_hash, group in context_groups.items():
            print(f"  Context {context_hash[:8]}... → {len(group)} requests")
    
        reuse_ratio = 1 - len(context_groups) / total_requests
        print(f"  → Total Requests: {total_requests}")
        print(f"  → Unique Contexts: {len(context_groups)}")
        print(f"  → Reuse Ratio: {reuse_ratio:.2f}")

        # First, process requests with contexts that are already cached
        cached_groups = []
        new_groups = []

        for context_hash, requests in context_groups.items():
            if context_hash in self.processed_contexts:
                cached_groups.append((context_hash, requests))
            else:
                new_groups.append((context_hash, requests))

        # Sort cached groups by number of requests (process larger groups first to maximize cache reuse)
        cached_groups.sort(key=lambda x: len(x[1]), reverse=True)

        # Sort new groups by average sequence length (process shorter contexts first)
        new_groups.sort(key=lambda x: np.mean([req.sequence_length for req in x[1]]))

        # Process cached groups first
        for context_hash, requests in cached_groups:
            # Sort requests within group by sequence length (shorter first)
            requests.sort(key=lambda x: x.sequence_length)
            scheduled_requests.extend(requests)

        # Then process new groups
        for context_hash, requests in new_groups:
            # Sort requests within group by sequence length (shorter first)
            requests.sort(key=lambda x: x.sequence_length)
            scheduled_requests.extend(requests)
            # Mark this context as processed
            self.processed_contexts.add(context_hash)

        return scheduled_requests

    def reset_cache_state(self):
        """Reset the scheduler's knowledge of cached contexts."""
        self.processed_contexts.clear()


class BatchProcessor:
    """Processes batches of requests and measures performance."""

    def __init__(self, server_ip: str, server_port: int):
        self.server_ip = server_ip
        self.server_port = server_port
        self.base_url = f"http://{server_ip}:{server_port}/v1"

    async def process_request(self, session: aiohttp.ClientSession, request: Request) -> Dict[str, Any]:
        """Process a single request and measure latency."""
        start_time = time.time()

        # Prepare the request payload
        payload = {
            "model": "Qwen/Qwen2.5-1.5B-Instruct",
            "messages": [
                {"role": "system", "content": request.context},
                {"role": "user", "content": request.question}
            ],
            "temperature": 0.5,
            "max_tokens": 100,
            "stream": False
        }

        try:
            async with session.post(f"{self.base_url}/chat/completions", json=payload) as response:
                if response.status == 200:
                    result = await response.json()
                    end_time = time.time()
                    latency = end_time - start_time

                    return {
                        "request_id": request.id,
                        "context_hash": request.context_hash,
                        "sequence_length": request.sequence_length,
                        "latency": latency,
                        "success": True,
                        "timestamp": start_time
                    }
                else:
                    return {
                        "request_id": request.id,
                        "context_hash": request.context_hash,
                        "sequence_length": request.sequence_length,
                        "latency": None,
                        "success": False,
                        "error": f"HTTP {response.status}",
                        "timestamp": start_time
                    }
        except Exception as e:
            end_time = time.time()
            return {
                "request_id": request.id,
                "context_hash": request.context_hash,
                "sequence_length": request.sequence_length,
                "latency": None,
                "success": False,
                "error": str(e),
                "timestamp": start_time
            }

    async def process_batch_sequential(self, scheduled_requests: List[Request]) -> List[Dict[str, Any]]:
        """Process requests sequentially (one after another)."""
        results = []

        async with aiohttp.ClientSession() as session:
            for request in scheduled_requests:
                result = await self.process_request(session, request)
                results.append(result)
                print(f"Processed {request.id}: {result['latency']:.3f}s" if result['success'] else f"Failed {request.id}")

        return results

    async def process_batch_parallel(self, scheduled_requests: List[Request], max_concurrent: int = 5) -> List[Dict[str, Any]]:
        """Process requests in parallel with limited concurrency."""
        semaphore = asyncio.Semaphore(max_concurrent)

        async def process_with_semaphore(session: aiohttp.ClientSession, request: Request):
            async with semaphore:
                return await self.process_request(session, request)

        async with aiohttp.ClientSession() as session:
            tasks = [process_with_semaphore(session, request) for request in scheduled_requests]
            results = await asyncio.gather(*tasks)

        return results


def main():
    import argparse

    parser = argparse.ArgumentParser(description='Batch Request Generator and Scheduler')
    parser.add_argument("--ip", required=True, help="Server IP address")
    parser.add_argument("--port", type=int, required=True, help="Server port")
    parser.add_argument("--num_batches", type=int, default=5, help="Number of batches to generate")
    parser.add_argument("--batch_size", type=int, default=10, help="Size of each batch")
    parser.add_argument("--repeat_ratio", type=float, default=0.3, help="Ratio of context reuse")
    parser.add_argument("--diversity", type=float, default=1.0, help="Context diversity level")
    parser.add_argument("--output", required=True, help="Output file for results")
    parser.add_argument("--processing_mode", choices=["sequential", "parallel"], default="sequential",
                        help="Request processing mode")
    parser.add_argument("--max_concurrent", type=int, default=5, help="Max concurrent requests for parallel mode")

    args = parser.parse_args()

    # Initialize components
    generator = BatchRequestGenerator()
    scheduler = RequestScheduler()
    processor = BatchProcessor(args.ip, args.port)

    # Load contexts and questions
    contexts = generator.load_contexts("data")
    questions = [
        "What is the main topic of this document?",
        "Summarize the content briefly.",
        "Explain the main concept in section 2.",
        "What are the key findings?",
        "How does this relate to current research?"
    ]

    print(f"Generating {args.num_batches} batches of size {args.batch_size}")

    # Generate batches
    batches = generator.generate_multiple_batches(
        contexts=contexts,
        questions=questions,
        num_batches=args.num_batches,
        batch_size=args.batch_size,
        repeat_ratio=args.repeat_ratio,
        diversity_level=args.diversity
    )

    # Process batches
    all_results = []

    async def process_all_batches():
        for i, batch in enumerate(batches):
            print(f"\nProcessing batch {i+1}/{len(batches)} (ID: {batch.id})")

            # Schedule the batch
            scheduled_requests = scheduler.schedule_batch(batch)

            # Process the scheduled requests
            if args.processing_mode == "sequential":
                results = await processor.process_batch_sequential(scheduled_requests)
            else:
                results = await processor.process_batch_parallel(scheduled_requests, args.max_concurrent)

            # Add batch metadata to results
            for result in results:
                result["batch_id"] = batch.id
                result["batch_index"] = i
                result["processing_mode"] = args.processing_mode

            all_results.extend(results)

    # Run the async processing
    asyncio.run(process_all_batches())

    # Calculate metrics
    successful_results = [r for r in all_results if r['success']]
    total_latency = sum(r['latency'] for r in successful_results)
    avg_latency = total_latency / len(successful_results) if successful_results else 0
    throughput = len(successful_results) / total_latency if total_latency > 0 else 0

    # Compile final results
    final_results = {
        "experiment_config": {
            "num_batches": args.num_batches,
            "batch_size": args.batch_size,
            "repeat_ratio": args.repeat_ratio,
            "diversity_level": args.diversity,
            "processing_mode": args.processing_mode,
            "max_concurrent": args.max_concurrent if args.processing_mode == "parallel" else None
        },
        "metrics": {
            "total_requests": len(all_results),
            "successful_requests": len(successful_results),
            "failed_requests": len(all_results) - len(successful_results),
            "average_latency": avg_latency,
            "total_latency": total_latency,
            "throughput": throughput
        },
        "detailed_results": all_results
    }

    # Save results
    with open(args.output, 'w') as f:
        json.dump(final_results, f, indent=2)

    print(f"\nResults saved to {args.output}")
    print(f"Average latency: {avg_latency:.3f}s")
    print(f"Throughput: {throughput:.2f} requests/second")
    print(f"Success rate: {len(successful_results)/len(all_results)*100:.1f}%")


if __name__ == "__main__":
    main()

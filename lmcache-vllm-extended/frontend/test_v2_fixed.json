{"experiment_config": {"num_batches": 2, "batch_size": 5, "repeat_ratio": 0.3, "diversity_level": 1.0, "processing_mode": "sequential", "max_concurrent": null, "api_version": "v2", "scheduling": "server-side"}, "metrics": {"total_requests": 10, "successful_requests": 0, "failed_requests": 10, "average_latency": 0, "total_latency": 0, "throughput": 0}, "detailed_results": [{"request_id": "req_1", "context_hash": "-4356943686345222129", "sequence_length": 507, "latency": null, "success": false, "error": "HTTP 400", "timestamp": 1748592813.8587904, "batch_id": "batch_13858", "batch_index": 0, "processing_mode": "sequential"}, {"request_id": "req_2", "context_hash": "4692233404520852608", "sequence_length": 469, "latency": null, "success": false, "error": "HTTP 400", "timestamp": 1748592813.8653774, "batch_id": "batch_13858", "batch_index": 0, "processing_mode": "sequential"}, {"request_id": "req_3", "context_hash": "8818872574932304886", "sequence_length": 536, "latency": null, "success": false, "error": "HTTP 400", "timestamp": 1748592813.8659892, "batch_id": "batch_13858", "batch_index": 0, "processing_mode": "sequential"}, {"request_id": "req_4", "context_hash": "8356526523470979472", "sequence_length": 458, "latency": null, "success": false, "error": "HTTP 400", "timestamp": 1748592813.8668132, "batch_id": "batch_13858", "batch_index": 0, "processing_mode": "sequential"}, {"request_id": "req_5", "context_hash": "8356526523470979472", "sequence_length": 461, "latency": null, "success": false, "error": "HTTP 400", "timestamp": 1748592813.8673096, "batch_id": "batch_13858", "batch_index": 0, "processing_mode": "sequential"}, {"request_id": "req_1", "context_hash": "3685864908995220763", "sequence_length": 451, "latency": null, "success": false, "error": "HTTP 400", "timestamp": 1748592813.8679087, "batch_id": "batch_13858", "batch_index": 1, "processing_mode": "sequential"}, {"request_id": "req_2", "context_hash": "8743485352333610856", "sequence_length": 458, "latency": null, "success": false, "error": "HTTP 400", "timestamp": 1748592813.8688047, "batch_id": "batch_13858", "batch_index": 1, "processing_mode": "sequential"}, {"request_id": "req_3", "context_hash": "-6099733442661763107", "sequence_length": 555, "latency": null, "success": false, "error": "HTTP 400", "timestamp": 1748592813.8692968, "batch_id": "batch_13858", "batch_index": 1, "processing_mode": "sequential"}, {"request_id": "req_4", "context_hash": "-4356943686345222129", "sequence_length": 505, "latency": null, "success": false, "error": "HTTP 400", "timestamp": 1748592813.8697813, "batch_id": "batch_13858", "batch_index": 1, "processing_mode": "sequential"}, {"request_id": "req_5", "context_hash": "8743485352333610856", "sequence_length": 456, "latency": null, "success": false, "error": "HTTP 400", "timestamp": 1748592813.8702383, "batch_id": "batch_13858", "batch_index": 1, "processing_mode": "sequential"}]}
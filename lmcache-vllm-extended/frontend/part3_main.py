# main testing in task3
import time
import part3_query_matcher
import pandas as pd
import part3_session


df = pd.read_parquet("gererated_queries.parquet")
generated_querys = {idx: row.to_numpy() for idx, row in df.iterrows()}

counter = 0
matched_counter = 0

time_search_start= []
time_search_end = []

time_session_start = []
time_session_end = []

search_time = []
session_time = []
whole_time = []

for idx, query in generated_querys.items():
    counter += 1
    clean_query = query[0].split('\n')[0]

    print(f"Query {idx}: {clean_query}")

    time_search_start.append(time.time())
    part3_query_matcher.get_approprite_context(clean_query)
    time_search_end.append(time.time())
    searched_chunk_name = ""
    with open("top_chunk.txt", "r") as f:
        searched_chunk_name = f.read().strip()

    if searched_chunk_name == idx:
        print(f"Query '{clean_query}' matched with the correct chunk: {searched_chunk_name}")
        matched_counter += 1

    # get the response from the chat session
    time_session_start.append(time.time())
    response = part3_session.get_response()
    time_session_end.append(time.time())
    time.sleep(5) 
    print("")

# print accuracy
accuracy = matched_counter / counter * 100
print(f"Total queries: {counter}, Matched queries: {matched_counter}, Accuracy: {accuracy:.2f}%")

# calculate time
search_time = [end - start for start, end in zip(time_search_start, time_search_end)]
session_time = [end - start for start, end in zip(time_session_start, time_session_end)]
whole_time = [search + session for search, session in zip(search_time, session_time)]

# print time
print(f"Average search time: {sum(search_time) / len(search_time):.4f} seconds")
print(f"Average session time: {sum(session_time) / len(session_time):.4f} seconds")
print(f"Average whole time: {sum(whole_time) / len(whole_time):.4f} seconds")

#print all the time in .2f 
print("Search times:", [f"{t:.2f}" for t in search_time])
print("Session times:", [f"{t:.2f}" for t in session_time])
print("Whole times:", [f"{t:.2f}" for t in whole_time])




#!/usr/bin/env python3
"""
Batch Processing Performance Analyzer

This script analyzes the performance of the batch processing system
and compares it with single-request processing.
"""

import json
import numpy as np
import pandas as pd
import matplotlib.pyplot as plt
import seaborn as sns
from typing import List, Dict, Any
import argparse
import os


class BatchAnalyzer:
    """Analyzes batch processing performance results."""

    def __init__(self):
        self.results = []
        self.df = None

    def load_results(self, file_paths: List[str]):
        """Load results from multiple JSON files."""
        for file_path in file_paths:
            try:
                with open(file_path, 'r') as f:
                    data = json.load(f)
                    data['source_file'] = file_path
                    self.results.append(data)
                print(f"Loaded results from {file_path}")
            except Exception as e:
                print(f"Error loading {file_path}: {e}")

    def create_dataframe(self):
        """Convert results to a pandas DataFrame for analysis."""
        rows = []

        for result in self.results:
            config = result.get('experiment_config', {})
            metrics = result.get('metrics', {})

            # Extract detailed results
            for detail in result.get('detailed_results', []):
                if detail.get('success', False):
                    row = {
                        # Experiment configuration
                        'num_batches': config.get('num_batches'),
                        'batch_size': config.get('batch_size'),
                        'repeat_ratio': config.get('repeat_ratio'),
                        'diversity_level': config.get('diversity_level'),
                        'processing_mode': config.get('processing_mode'),
                        'max_concurrent': config.get('max_concurrent'),

                        # Request details
                        'request_id': detail.get('request_id'),
                        'context_hash': detail.get('context_hash'),
                        'sequence_length': detail.get('sequence_length'),
                        'latency': detail.get('latency'),
                        'batch_id': detail.get('batch_id'),
                        'batch_index': detail.get('batch_index'),

                        # Overall metrics
                        'overall_throughput': metrics.get('throughput'),
                        'overall_avg_latency': metrics.get('average_latency'),
                        'success_rate': metrics.get('successful_requests') / metrics.get('total_requests') * 100
                    }
                    rows.append(row)

        self.df = pd.DataFrame(rows)
        return self.df

    def analyze_scheduler_effectiveness(self, output_dir: str):
        """Analyze how effective the scheduler is at grouping similar contexts."""
        if self.df is None:
            self.create_dataframe()

        if self.df.empty:
            print("No successful requests found for scheduler effectiveness analysis")
            return None

        # Group by context_hash and batch to see clustering
        context_analysis = []

        for batch_id in self.df['batch_id'].unique():
            batch_data = self.df[self.df['batch_id'] == batch_id]

            # Count unique contexts in this batch
            unique_contexts = batch_data['context_hash'].nunique()
            total_requests = len(batch_data)

            # Calculate context reuse ratio
            context_reuse_ratio = 1 - (unique_contexts / total_requests)

            # Calculate average latency for this batch
            avg_latency = batch_data['latency'].mean()

            context_analysis.append({
                'batch_id': batch_id,
                'unique_contexts': unique_contexts,
                'total_requests': total_requests,
                'context_reuse_ratio': context_reuse_ratio,
                'avg_latency': avg_latency,
                'batch_size': batch_data['batch_size'].iloc[0],
                'repeat_ratio': batch_data['repeat_ratio'].iloc[0]
            })

        context_df = pd.DataFrame(context_analysis)

        # Plot context reuse vs latency
        plt.figure(figsize=(10, 6))
        plt.scatter(context_df['context_reuse_ratio'], context_df['avg_latency'], alpha=0.7)
        plt.xlabel('Context Reuse Ratio')
        plt.ylabel('Average Batch Latency (seconds)')
        plt.title('Context Reuse vs Batch Latency')
        plt.grid(True, alpha=0.3)

        # Add trend line
        z = np.polyfit(context_df['context_reuse_ratio'], context_df['avg_latency'], 1)
        p = np.poly1d(z)
        plt.plot(context_df['context_reuse_ratio'], p(context_df['context_reuse_ratio']), "r--", alpha=0.8)

        os.makedirs(output_dir, exist_ok=True)
        plt.savefig(os.path.join(output_dir, 'context_reuse_vs_latency.png'))
        plt.close()

        return context_df

    def analyze_batch_size_impact(self, output_dir: str):
        """Analyze how batch size affects performance."""
        if self.df is None:
            self.create_dataframe()

        if self.df.empty:
            print("No successful requests found for batch size analysis")
            return None

        # Group by batch size
        batch_size_analysis = self.df.groupby('batch_size').agg({
            'latency': ['mean', 'std'],
            'overall_throughput': 'mean',
            'sequence_length': 'mean'
        }).round(3)

        # Flatten column names
        batch_size_analysis.columns = ['_'.join(col).strip() for col in batch_size_analysis.columns]
        batch_size_analysis = batch_size_analysis.reset_index()

        # Plot batch size vs latency
        plt.figure(figsize=(12, 5))

        plt.subplot(1, 2, 1)
        plt.errorbar(batch_size_analysis['batch_size'],
                    batch_size_analysis['latency_mean'],
                    yerr=batch_size_analysis['latency_std'],
                    marker='o', capsize=5)
        plt.xlabel('Batch Size')
        plt.ylabel('Average Latency (seconds)')
        plt.title('Batch Size vs Latency')
        plt.grid(True, alpha=0.3)

        plt.subplot(1, 2, 2)
        plt.plot(batch_size_analysis['batch_size'],
                batch_size_analysis['overall_throughput_mean'],
                marker='o')
        plt.xlabel('Batch Size')
        plt.ylabel('Throughput (requests/second)')
        plt.title('Batch Size vs Throughput')
        plt.grid(True, alpha=0.3)

        plt.tight_layout()
        os.makedirs(output_dir, exist_ok=True)
        plt.savefig(os.path.join(output_dir, 'batch_size_analysis.png'))
        plt.close()

        return batch_size_analysis

    def analyze_diversity_impact(self, output_dir: str):
        """Analyze how request diversity affects performance."""
        if self.df is None:
            self.create_dataframe()

        if self.df.empty:
            print("No successful requests found for diversity analysis")
            return None

        # Group by diversity level
        diversity_analysis = self.df.groupby('diversity_level').agg({
            'latency': ['mean', 'std'],
            'overall_throughput': 'mean',
            'sequence_length': 'mean'
        }).round(3)

        # Flatten column names
        diversity_analysis.columns = ['_'.join(col).strip() for col in diversity_analysis.columns]
        diversity_analysis = diversity_analysis.reset_index()

        # Plot diversity vs performance
        plt.figure(figsize=(12, 5))

        plt.subplot(1, 2, 1)
        plt.errorbar(diversity_analysis['diversity_level'],
                    diversity_analysis['latency_mean'],
                    yerr=diversity_analysis['latency_std'],
                    marker='o', capsize=5)
        plt.xlabel('Diversity Level')
        plt.ylabel('Average Latency (seconds)')
        plt.title('Request Diversity vs Latency')
        plt.grid(True, alpha=0.3)

        plt.subplot(1, 2, 2)
        plt.plot(diversity_analysis['diversity_level'],
                diversity_analysis['overall_throughput_mean'],
                marker='o')
        plt.xlabel('Diversity Level')
        plt.ylabel('Throughput (requests/second)')
        plt.title('Request Diversity vs Throughput')
        plt.grid(True, alpha=0.3)

        plt.tight_layout()
        os.makedirs(output_dir, exist_ok=True)
        plt.savefig(os.path.join(output_dir, 'diversity_analysis.png'))
        plt.close()

        return diversity_analysis

    def analyze_sequence_length_impact(self, output_dir: str):
        """Analyze how sequence length affects batch processing."""
        if self.df is None:
            self.create_dataframe()

        if self.df.empty:
            print("No successful requests found for sequence length analysis")
            return None

        # Create sequence length bins based on actual data distribution
        # Use custom bins that make sense for our data
        seq_lengths = self.df['sequence_length']
        min_len, max_len = seq_lengths.min(), seq_lengths.max()

        # Define meaningful bins based on the data distribution
        if max_len > 10000:  # We have very long sequences
            bins = [0, 700, 900, 1200, 10000, float('inf')]
            labels = ['Short (<700)', 'Medium (700-900)', 'Long (900-1200)', 'Very Long (1200-10K)', 'Ultra Long (>10K)']
        else:
            bins = [0, 700, 800, 900, 1000, float('inf')]
            labels = ['Short (<700)', 'Medium-Short (700-800)', 'Medium (800-900)', 'Medium-Long (900-1000)', 'Long (>1000)']

        self.df['seq_length_bin'] = pd.cut(self.df['sequence_length'], bins=bins, labels=labels, include_lowest=True)

        # Group by sequence length bins and filter out empty bins
        seq_analysis = self.df.groupby('seq_length_bin', observed=True).agg({
            'latency': ['mean', 'std', 'count'],
            'sequence_length': ['mean', 'min', 'max']
        }).round(3)

        # Only plot bins that have data
        seq_means = self.df.groupby('seq_length_bin', observed=True)['latency'].mean()
        seq_stds = self.df.groupby('seq_length_bin', observed=True)['latency'].std()
        seq_counts = self.df.groupby('seq_length_bin', observed=True)['latency'].count()

        # Plot sequence length vs latency
        plt.figure(figsize=(12, 6))

        # Filter out bins with too few samples (less than 5)
        valid_bins = seq_counts >= 5
        if valid_bins.sum() == 0:
            print("Not enough data points for sequence length analysis")
            return seq_analysis

        filtered_means = seq_means[valid_bins]
        filtered_stds = seq_stds[valid_bins]
        filtered_counts = seq_counts[valid_bins]

        plt.errorbar(range(len(filtered_means)), filtered_means.values,
                    yerr=filtered_stds.values, marker='o', capsize=5, linewidth=2, markersize=8)

        # Add count annotations
        for i, (mean_val, count) in enumerate(zip(filtered_means.values, filtered_counts.values)):
            plt.annotate(f'n={count}', (i, mean_val), textcoords="offset points",
                        xytext=(0,10), ha='center', fontsize=9)

        plt.xticks(range(len(filtered_means)), filtered_means.index, rotation=45, ha='right')
        plt.xlabel('Sequence Length Category')
        plt.ylabel('Average Latency (seconds)')
        plt.title('Sequence Length vs Latency in Batch Processing')
        plt.grid(True, alpha=0.3)
        plt.tight_layout()

        os.makedirs(output_dir, exist_ok=True)
        plt.savefig(os.path.join(output_dir, 'sequence_length_analysis.png'), dpi=300, bbox_inches='tight')
        plt.close()

        return seq_analysis

    def compare_processing_modes(self, output_dir: str):
        """Compare sequential vs parallel processing modes."""
        if self.df is None:
            self.create_dataframe()

        if self.df.empty:
            print("No successful requests found for processing mode comparison")
            return None

        if 'processing_mode' not in self.df.columns:
            print("Processing mode data not available")
            return None

        # Group by processing mode
        mode_analysis = self.df.groupby('processing_mode').agg({
            'latency': ['mean', 'std'],
            'overall_throughput': 'mean',
            'success_rate': 'mean'
        }).round(3)

        # Plot comparison
        plt.figure(figsize=(12, 4))

        plt.subplot(1, 3, 1)
        modes = mode_analysis.index
        latencies = mode_analysis[('latency', 'mean')]
        latency_stds = mode_analysis[('latency', 'std')]

        plt.bar(modes, latencies, yerr=latency_stds, capsize=5, alpha=0.7)
        plt.ylabel('Average Latency (seconds)')
        plt.title('Processing Mode vs Latency')
        plt.xticks(rotation=45)

        plt.subplot(1, 3, 2)
        throughputs = mode_analysis[('overall_throughput', 'mean')]
        plt.bar(modes, throughputs, alpha=0.7)
        plt.ylabel('Throughput (requests/second)')
        plt.title('Processing Mode vs Throughput')
        plt.xticks(rotation=45)

        plt.subplot(1, 3, 3)
        success_rates = mode_analysis[('success_rate', 'mean')]
        plt.bar(modes, success_rates, alpha=0.7)
        plt.ylabel('Success Rate (%)')
        plt.title('Processing Mode vs Success Rate')
        plt.xticks(rotation=45)

        plt.tight_layout()
        os.makedirs(output_dir, exist_ok=True)
        plt.savefig(os.path.join(output_dir, 'processing_mode_comparison.png'))
        plt.close()

        return mode_analysis

    def generate_summary_report(self, output_dir: str):
        """Generate a comprehensive summary report."""
        if self.df is None:
            self.create_dataframe()

        if self.df.empty:
            # Generate report for failed experiments
            overall_stats = {
                'total_experiments': len(self.results),
                'total_requests_processed': 0,
                'average_latency': 0,
                'latency_std': 0,
                'average_throughput': 0,
                'average_success_rate': 0,
                'sequence_length_range': (0, 0),
                'batch_size_range': (0, 0)
            }
        else:
            # Calculate overall statistics
            overall_stats = {
                'total_experiments': len(self.results),
                'total_requests_processed': len(self.df),
                'average_latency': self.df['latency'].mean(),
                'latency_std': self.df['latency'].std(),
                'average_throughput': self.df['overall_throughput'].mean(),
                'average_success_rate': self.df['success_rate'].mean(),
                'sequence_length_range': (self.df['sequence_length'].min(), self.df['sequence_length'].max()),
                'batch_size_range': (self.df['batch_size'].min(), self.df['batch_size'].max())
            }

        # Create summary report
        report = f"""
# Batch Processing Performance Analysis Report

## Overall Statistics
- Total Experiments: {overall_stats['total_experiments']}
- Total Requests Processed: {overall_stats['total_requests_processed']}
- Average Latency: {overall_stats['average_latency']:.3f} ± {overall_stats['latency_std']:.3f} seconds
- Average Throughput: {overall_stats['average_throughput']:.2f} requests/second
- Average Success Rate: {overall_stats['average_success_rate']:.1f}%
- Sequence Length Range: {overall_stats['sequence_length_range'][0]} - {overall_stats['sequence_length_range'][1]} tokens
- Batch Size Range: {overall_stats['batch_size_range'][0]} - {overall_stats['batch_size_range'][1]} requests

## Key Findings

### Scheduler Effectiveness
The scheduler groups requests with similar contexts to maximize cache reuse.
Context reuse ratio correlates with improved latency performance.

### Batch Size Impact
Larger batch sizes may improve throughput but can increase individual request latency.
Optimal batch size depends on the balance between throughput and latency requirements.

### Request Diversity Impact
Higher diversity (more unique contexts) reduces cache hit rates and increases latency.
Lower diversity improves performance through better cache utilization.

### Sequence Length Impact
Longer sequences generally result in higher latency.
The scheduler's effectiveness may vary with sequence length distribution.

## Recommendations
1. Use moderate batch sizes (10-20 requests) for balanced performance
2. Group requests with similar contexts when possible
3. Consider sequence length distribution when batching
4. Monitor cache hit rates to optimize scheduler performance
"""

        # Save report
        os.makedirs(output_dir, exist_ok=True)
        with open(os.path.join(output_dir, 'analysis_report.md'), 'w') as f:
            f.write(report)

        return overall_stats


def main():
    parser = argparse.ArgumentParser(description='Analyze batch processing performance')
    parser.add_argument('--input_files', nargs='+', required=True, help='Input JSON result files')
    parser.add_argument('--output_dir', default='batch_analysis', help='Output directory for plots and reports')

    args = parser.parse_args()

    # Initialize analyzer
    analyzer = BatchAnalyzer()

    # Load results
    analyzer.load_results(args.input_files)

    if not analyzer.results:
        print("No valid results found")
        return

    # Create DataFrame
    df = analyzer.create_dataframe()
    print(f"Loaded {len(df)} request records from {len(analyzer.results)} experiments")
    "debug"
    print("\n[Debug] Latency describe for batch size = 15:")
    print(df[df['batch_size'] == 15]['latency'].describe())

    # Run analyses
    print("Analyzing scheduler effectiveness...")
    analyzer.analyze_scheduler_effectiveness(args.output_dir)

    print("Analyzing batch size impact...")
    analyzer.analyze_batch_size_impact(args.output_dir)

    print("Analyzing diversity impact...")
    analyzer.analyze_diversity_impact(args.output_dir)

    print("Analyzing sequence length impact...")
    analyzer.analyze_sequence_length_impact(args.output_dir)

    print("Comparing processing modes...")
    analyzer.compare_processing_modes(args.output_dir)

    print("Generating summary report...")
    analyzer.generate_summary_report(args.output_dir)

    print(f"Analysis complete. Results saved to {args.output_dir}/")


if __name__ == "__main__":
    main()

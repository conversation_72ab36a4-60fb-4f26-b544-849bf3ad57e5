from safetensors.torch import load_file
kv_cache_file = "/home/<USER>/group3/lmcache-server/vllm@Qwen-Qwen2.5-1.5B-Instruct@1@<EMAIL>"
kv_cache = load_file(kv_cache_file)  # 返回一个 dict，key 对应 tensor 名，value 是 tensor
kv = kv_cache['tensor_bytes']  # shape: [28, 35, 2, 128]
print(kv.shape)  # [28, 35, 2, 128]

num_layers, kv_type, seq_len, num_heads, head_dim = kv.shape

key_tensor = kv[:, 0]  # shape: [28, 35, 2, 128]
value_tensor = kv[:, 1]  # shape: [28, 35, 2, 128]

# 我们可以对 seq_len 和 heads 做平均，得到每层一个 [128] 向量
value_pooled = value_tensor.mean(dim=1).mean(dim=1)  # shape: [28, 128]

final_vector = value_pooled.flatten()  # shape: [3584]

print(final_vector.shape)  # [3584]
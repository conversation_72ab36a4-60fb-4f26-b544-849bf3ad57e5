{"experiment_config": {"num_batches": 2, "batch_size": 5, "repeat_ratio": 0.3, "diversity_level": 1.0, "processing_mode": "sequential", "max_concurrent": null, "api_version": "v2", "scheduling": "server-side"}, "metrics": {"total_requests": 10, "successful_requests": 0, "failed_requests": 10, "average_latency": 0, "total_latency": 0, "throughput": 0}, "detailed_results": [{"request_id": "req_1", "context_hash": "2204361664443550595", "sequence_length": 495, "latency": null, "success": false, "error": "HTTP 400", "timestamp": 1748592733.253165, "batch_id": "batch_33252", "batch_index": 0, "processing_mode": "sequential"}, {"request_id": "req_2", "context_hash": "2414525476359999340", "sequence_length": 536, "latency": null, "success": false, "error": "HTTP 400", "timestamp": 1748592733.2582376, "batch_id": "batch_33252", "batch_index": 0, "processing_mode": "sequential"}, {"request_id": "req_3", "context_hash": "1401022910735172718", "sequence_length": 505, "latency": null, "success": false, "error": "HTTP 400", "timestamp": 1748592733.2590222, "batch_id": "batch_33252", "batch_index": 0, "processing_mode": "sequential"}, {"request_id": "req_4", "context_hash": "1401022910735172718", "sequence_length": 507, "latency": null, "success": false, "error": "HTTP 400", "timestamp": 1748592733.2595935, "batch_id": "batch_33252", "batch_index": 0, "processing_mode": "sequential"}, {"request_id": "req_5", "context_hash": "-2920932990156234120", "sequence_length": 552, "latency": null, "success": false, "error": "HTTP 400", "timestamp": 1748592733.2601433, "batch_id": "batch_33252", "batch_index": 0, "processing_mode": "sequential"}, {"request_id": "req_1", "context_hash": "4323225308464531841", "sequence_length": 458, "latency": null, "success": false, "error": "HTTP 400", "timestamp": 1748592733.2608025, "batch_id": "batch_33252", "batch_index": 1, "processing_mode": "sequential"}, {"request_id": "req_2", "context_hash": "2414525476359999340", "sequence_length": 539, "latency": null, "success": false, "error": "HTTP 400", "timestamp": 1748592733.2617483, "batch_id": "batch_33252", "batch_index": 1, "processing_mode": "sequential"}, {"request_id": "req_3", "context_hash": "5609152425314351377", "sequence_length": 483, "latency": null, "success": false, "error": "HTTP 400", "timestamp": 1748592733.2622468, "batch_id": "batch_33252", "batch_index": 1, "processing_mode": "sequential"}, {"request_id": "req_4", "context_hash": "1401022910735172718", "sequence_length": 505, "latency": null, "success": false, "error": "HTTP 400", "timestamp": 1748592733.2627876, "batch_id": "batch_33252", "batch_index": 1, "processing_mode": "sequential"}, {"request_id": "req_5", "context_hash": "4323225308464531841", "sequence_length": 458, "latency": null, "success": false, "error": "HTTP 400", "timestamp": 1748592733.2635052, "batch_id": "batch_33252", "batch_index": 1, "processing_mode": "sequential"}]}
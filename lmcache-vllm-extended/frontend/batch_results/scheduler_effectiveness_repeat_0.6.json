{"experiment_config": {"num_batches": 5, "batch_size": 10, "repeat_ratio": 0.6, "diversity_level": 1.0, "processing_mode": "sequential", "max_concurrent": null}, "metrics": {"total_requests": 50, "successful_requests": 50, "failed_requests": 0, "average_latency": 0.3686817264556885, "total_latency": 18.434086322784424, "throughput": 2.712366597643643}, "detailed_results": [{"request_id": "req_2", "context_hash": "696164233548113142", "sequence_length": 765, "latency": 0.5977587699890137, "success": true, "timestamp": 1748476019.177294, "batch_id": "batch_1", "batch_index": 0, "processing_mode": "sequential"}, {"request_id": "req_4", "context_hash": "-4744396994753484027", "sequence_length": 807, "latency": 0.04303765296936035, "success": true, "timestamp": 1748476019.7750945, "batch_id": "batch_1", "batch_index": 0, "processing_mode": "sequential"}, {"request_id": "req_9", "context_hash": "-4744396994753484027", "sequence_length": 808, "latency": 0.0419158935546875, "success": true, "timestamp": 1748476019.8181567, "batch_id": "batch_1", "batch_index": 0, "processing_mode": "sequential"}, {"request_id": "req_8", "context_hash": "-4744396994753484027", "sequence_length": 808, "latency": 0.0415806770324707, "success": true, "timestamp": 1748476019.8600943, "batch_id": "batch_1", "batch_index": 0, "processing_mode": "sequential"}, {"request_id": "req_6", "context_hash": "2041100442072835020", "sequence_length": 882, "latency": 0.08991360664367676, "success": true, "timestamp": 1748476019.9016924, "batch_id": "batch_1", "batch_index": 0, "processing_mode": "sequential"}, {"request_id": "req_1", "context_hash": "2041100442072835020", "sequence_length": 884, "latency": 0.12420415878295898, "success": true, "timestamp": 1748476019.9916239, "batch_id": "batch_1", "batch_index": 0, "processing_mode": "sequential"}, {"request_id": "req_10", "context_hash": "2041100442072835020", "sequence_length": 885, "latency": 0.14119434356689453, "success": true, "timestamp": 1748476020.1158454, "batch_id": "batch_1", "batch_index": 0, "processing_mode": "sequential"}, {"request_id": "req_5", "context_hash": "-8867903811260842836", "sequence_length": 893, "latency": 0.14637994766235352, "success": true, "timestamp": 1748476020.257057, "batch_id": "batch_1", "batch_index": 0, "processing_mode": "sequential"}, {"request_id": "req_3", "context_hash": "-8867903811260842836", "sequence_length": 894, "latency": 0.04359316825866699, "success": true, "timestamp": 1748476020.4034522, "batch_id": "batch_1", "batch_index": 0, "processing_mode": "sequential"}, {"request_id": "req_7", "context_hash": "-8867903811260842836", "sequence_length": 894, "latency": 0.04278755187988281, "success": true, "timestamp": 1748476020.4470627, "batch_id": "batch_1", "batch_index": 0, "processing_mode": "sequential"}, {"request_id": "req_18", "context_hash": "2041100442072835020", "sequence_length": 883, "latency": 0.21967458724975586, "success": true, "timestamp": 1748476020.4902194, "batch_id": "batch_2", "batch_index": 1, "processing_mode": "sequential"}, {"request_id": "req_15", "context_hash": "2041100442072835020", "sequence_length": 883, "latency": 0.23743963241577148, "success": true, "timestamp": 1748476020.7099204, "batch_id": "batch_2", "batch_index": 1, "processing_mode": "sequential"}, {"request_id": "req_12", "context_hash": "2041100442072835020", "sequence_length": 885, "latency": 0.0599064826965332, "success": true, "timestamp": 1748476020.947385, "batch_id": "batch_2", "batch_index": 1, "processing_mode": "sequential"}, {"request_id": "req_14", "context_hash": "6482221895980069649", "sequence_length": 607, "latency": 1.7298436164855957, "success": true, "timestamp": 1748476021.0073133, "batch_id": "batch_2", "batch_index": 1, "processing_mode": "sequential"}, {"request_id": "req_20", "context_hash": "6482221895980069649", "sequence_length": 610, "latency": 1.2697241306304932, "success": true, "timestamp": 1748476022.7371874, "batch_id": "batch_2", "batch_index": 1, "processing_mode": "sequential"}, {"request_id": "req_11", "context_hash": "3926437746446514605", "sequence_length": 906, "latency": 0.04569292068481445, "success": true, "timestamp": 1748476024.0069427, "batch_id": "batch_2", "batch_index": 1, "processing_mode": "sequential"}, {"request_id": "req_16", "context_hash": "3926437746446514605", "sequence_length": 906, "latency": 0.042746543884277344, "success": true, "timestamp": 1748476024.05266, "batch_id": "batch_2", "batch_index": 1, "processing_mode": "sequential"}, {"request_id": "req_19", "context_hash": "3926437746446514605", "sequence_length": 907, "latency": 0.0422816276550293, "success": true, "timestamp": 1748476024.0954263, "batch_id": "batch_2", "batch_index": 1, "processing_mode": "sequential"}, {"request_id": "req_17", "context_hash": "3926437746446514605", "sequence_length": 907, "latency": 0.04285478591918945, "success": true, "timestamp": 1748476024.137728, "batch_id": "batch_2", "batch_index": 1, "processing_mode": "sequential"}, {"request_id": "req_13", "context_hash": "-433186536387239467", "sequence_length": 924, "latency": 1.1501564979553223, "success": true, "timestamp": 1748476024.1805995, "batch_id": "batch_2", "batch_index": 1, "processing_mode": "sequential"}, {"request_id": "req_21", "context_hash": "-4744396994753484027", "sequence_length": 807, "latency": 0.04274916648864746, "success": true, "timestamp": 1748476025.3310978, "batch_id": "batch_3", "batch_index": 2, "processing_mode": "sequential"}, {"request_id": "req_27", "context_hash": "-4744396994753484027", "sequence_length": 807, "latency": 0.042046546936035156, "success": true, "timestamp": 1748476025.3738766, "batch_id": "batch_3", "batch_index": 2, "processing_mode": "sequential"}, {"request_id": "req_25", "context_hash": "-4744396994753484027", "sequence_length": 808, "latency": 0.07367587089538574, "success": true, "timestamp": 1748476025.4159417, "batch_id": "batch_3", "batch_index": 2, "processing_mode": "sequential"}, {"request_id": "req_26", "context_hash": "-4744396994753484027", "sequence_length": 810, "latency": 1.0666322708129883, "success": true, "timestamp": 1748476025.4896355, "batch_id": "batch_3", "batch_index": 2, "processing_mode": "sequential"}, {"request_id": "req_28", "context_hash": "-3746353848088158098", "sequence_length": 679, "latency": 0.13919973373413086, "success": true, "timestamp": 1748476026.5562963, "batch_id": "batch_3", "batch_index": 2, "processing_mode": "sequential"}, {"request_id": "req_23", "context_hash": "-3746353848088158098", "sequence_length": 682, "latency": 0.08958792686462402, "success": true, "timestamp": 1748476026.6955192, "batch_id": "batch_3", "batch_index": 2, "processing_mode": "sequential"}, {"request_id": "req_24", "context_hash": "-4328775583155921756", "sequence_length": 790, "latency": 0.13927960395812988, "success": true, "timestamp": 1748476026.7851272, "batch_id": "batch_3", "batch_index": 2, "processing_mode": "sequential"}, {"request_id": "req_29", "context_hash": "-4328775583155921756", "sequence_length": 791, "latency": 0.1067051887512207, "success": true, "timestamp": 1748476026.9244237, "batch_id": "batch_3", "batch_index": 2, "processing_mode": "sequential"}, {"request_id": "req_30", "context_hash": "-4328775583155921756", "sequence_length": 793, "latency": 0.6775445938110352, "success": true, "timestamp": 1748476027.0311537, "batch_id": "batch_3", "batch_index": 2, "processing_mode": "sequential"}, {"request_id": "req_22", "context_hash": "-884582226823706062", "sequence_length": 832, "latency": 0.8016276359558105, "success": true, "timestamp": 1748476027.7087216, "batch_id": "batch_3", "batch_index": 2, "processing_mode": "sequential"}, {"request_id": "req_32", "context_hash": "-3746353848088158098", "sequence_length": 679, "latency": 0.1387622356414795, "success": true, "timestamp": 1748476028.510702, "batch_id": "batch_4", "batch_index": 3, "processing_mode": "sequential"}, {"request_id": "req_37", "context_hash": "-3746353848088158098", "sequence_length": 681, "latency": 0.10643839836120605, "success": true, "timestamp": 1748476028.6494865, "batch_id": "batch_4", "batch_index": 3, "processing_mode": "sequential"}, {"request_id": "req_39", "context_hash": "-3746353848088158098", "sequence_length": 682, "latency": 0.04170083999633789, "success": true, "timestamp": 1748476028.7559435, "batch_id": "batch_4", "batch_index": 3, "processing_mode": "sequential"}, {"request_id": "req_35", "context_hash": "-4744396994753484027", "sequence_length": 807, "latency": 0.4314427375793457, "success": true, "timestamp": 1748476028.7976625, "batch_id": "batch_4", "batch_index": 3, "processing_mode": "sequential"}, {"request_id": "req_31", "context_hash": "-4744396994753484027", "sequence_length": 808, "latency": 0.0436251163482666, "success": true, "timestamp": 1748476029.229123, "batch_id": "batch_4", "batch_index": 3, "processing_mode": "sequential"}, {"request_id": "req_40", "context_hash": "-4744396994753484027", "sequence_length": 809, "latency": 0.04166865348815918, "success": true, "timestamp": 1748476029.272764, "batch_id": "batch_4", "batch_index": 3, "processing_mode": "sequential"}, {"request_id": "req_36", "context_hash": "2041100442072835020", "sequence_length": 882, "latency": 0.09058690071105957, "success": true, "timestamp": 1748476029.3144488, "batch_id": "batch_4", "batch_index": 3, "processing_mode": "sequential"}, {"request_id": "req_33", "context_hash": "2041100442072835020", "sequence_length": 885, "latency": 0.10772418975830078, "success": true, "timestamp": 1748476029.405051, "batch_id": "batch_4", "batch_index": 3, "processing_mode": "sequential"}, {"request_id": "req_38", "context_hash": "6007248615135706619", "sequence_length": 910, "latency": 0.043433189392089844, "success": true, "timestamp": 1748476029.5127916, "batch_id": "batch_4", "batch_index": 3, "processing_mode": "sequential"}, {"request_id": "req_34", "context_hash": "6007248615135706619", "sequence_length": 910, "latency": 0.04281210899353027, "success": true, "timestamp": 1748476029.556242, "batch_id": "batch_4", "batch_index": 3, "processing_mode": "sequential"}, {"request_id": "req_49", "context_hash": "2041100442072835020", "sequence_length": 883, "latency": 0.09067535400390625, "success": true, "timestamp": 1748476029.599315, "batch_id": "batch_5", "batch_index": 4, "processing_mode": "sequential"}, {"request_id": "req_42", "context_hash": "2041100442072835020", "sequence_length": 884, "latency": 0.09130358695983887, "success": true, "timestamp": 1748476029.6900084, "batch_id": "batch_5", "batch_index": 4, "processing_mode": "sequential"}, {"request_id": "req_47", "context_hash": "2041100442072835020", "sequence_length": 884, "latency": 0.09166383743286133, "success": true, "timestamp": 1748476029.781329, "batch_id": "batch_5", "batch_index": 4, "processing_mode": "sequential"}, {"request_id": "req_44", "context_hash": "-3746353848088158098", "sequence_length": 681, "latency": 0.07325983047485352, "success": true, "timestamp": 1748476029.8730102, "batch_id": "batch_5", "batch_index": 4, "processing_mode": "sequential"}, {"request_id": "req_48", "context_hash": "-3746353848088158098", "sequence_length": 682, "latency": 0.04204368591308594, "success": true, "timestamp": 1748476029.946286, "batch_id": "batch_5", "batch_index": 4, "processing_mode": "sequential"}, {"request_id": "req_50", "context_hash": "-4328775583155921756", "sequence_length": 793, "latency": 0.057808637619018555, "success": true, "timestamp": 1748476029.9883463, "batch_id": "batch_5", "batch_index": 4, "processing_mode": "sequential"}, {"request_id": "req_43", "context_hash": "-4328775583155921756", "sequence_length": 793, "latency": 0.25357842445373535, "success": true, "timestamp": 1748476030.0461705, "batch_id": "batch_5", "batch_index": 4, "processing_mode": "sequential"}, {"request_id": "req_41", "context_hash": "-692836735410152896", "sequence_length": 21001, "latency": 2.5607762336730957, "success": true, "timestamp": 1748476030.2997644, "batch_id": "batch_5", "batch_index": 4, "processing_mode": "sequential"}, {"request_id": "req_46", "context_hash": "-692836735410152896", "sequence_length": 21001, "latency": 2.3732266426086426, "success": true, "timestamp": 1748476032.860572, "batch_id": "batch_5", "batch_index": 4, "processing_mode": "sequential"}, {"request_id": "req_45", "context_hash": "-692836735410152896", "sequence_length": 21001, "latency": 2.3798205852508545, "success": true, "timestamp": 1748476035.233831, "batch_id": "batch_5", "batch_index": 4, "processing_mode": "sequential"}]}
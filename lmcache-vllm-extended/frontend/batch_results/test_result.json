{"experiment_config": {"num_batches": 3, "batch_size": 5, "repeat_ratio": 0.3, "diversity_level": 1.0, "processing_mode": "sequential", "max_concurrent": null}, "metrics": {"total_requests": 15, "successful_requests": 15, "failed_requests": 0, "average_latency": 1.8852215131123862, "total_latency": 28.27832269668579, "throughput": 0.5304416446792297}, "detailed_results": [{"request_id": "req_3", "context_hash": "5517569330621099588", "sequence_length": 667, "latency": 0.981482744216919, "success": true, "timestamp": 1748470105.550873, "batch_id": "batch_1", "batch_index": 0, "processing_mode": "sequential"}, {"request_id": "req_4", "context_hash": "-6390654239086850407", "sequence_length": 681, "latency": 0.9749081134796143, "success": true, "timestamp": 1748470106.532406, "batch_id": "batch_1", "batch_index": 0, "processing_mode": "sequential"}, {"request_id": "req_2", "context_hash": "2632923814701490577", "sequence_length": 832, "latency": 1.674079179763794, "success": true, "timestamp": 1748470107.5073588, "batch_id": "batch_1", "batch_index": 0, "processing_mode": "sequential"}, {"request_id": "req_5", "context_hash": "5338010343620962780", "sequence_length": 924, "latency": 1.0402796268463135, "success": true, "timestamp": 1748470109.1814682, "batch_id": "batch_1", "batch_index": 0, "processing_mode": "sequential"}, {"request_id": "req_1", "context_hash": "5338010343620962780", "sequence_length": 925, "latency": 0.14162421226501465, "success": true, "timestamp": 1748470110.2217784, "batch_id": "batch_1", "batch_index": 0, "processing_mode": "sequential"}, {"request_id": "req_6", "context_hash": "5338010343620962780", "sequence_length": 925, "latency": 0.9848372936248779, "success": true, "timestamp": 1748470110.3638394, "batch_id": "batch_2", "batch_index": 1, "processing_mode": "sequential"}, {"request_id": "req_10", "context_hash": "5338010343620962780", "sequence_length": 925, "latency": 0.989046573638916, "success": true, "timestamp": 1748470111.3487136, "batch_id": "batch_2", "batch_index": 1, "processing_mode": "sequential"}, {"request_id": "req_9", "context_hash": "2342297907129627517", "sequence_length": 610, "latency": 1.129615306854248, "success": true, "timestamp": 1748470112.3377936, "batch_id": "batch_2", "batch_index": 1, "processing_mode": "sequential"}, {"request_id": "req_7", "context_hash": "1101384674348208841", "sequence_length": 904, "latency": 14.612001419067383, "success": true, "timestamp": 1748470113.4674354, "batch_id": "batch_2", "batch_index": 1, "processing_mode": "sequential"}, {"request_id": "req_8", "context_hash": "3938466312615193811", "sequence_length": 910, "latency": 0.41109347343444824, "success": true, "timestamp": 1748470128.0794773, "batch_id": "batch_2", "batch_index": 1, "processing_mode": "sequential"}, {"request_id": "req_15", "context_hash": "3938466312615193811", "sequence_length": 910, "latency": 0.9377679824829102, "success": true, "timestamp": 1748470128.490928, "batch_id": "batch_3", "batch_index": 2, "processing_mode": "sequential"}, {"request_id": "req_14", "context_hash": "3938466312615193811", "sequence_length": 912, "latency": 1.656661033630371, "success": true, "timestamp": 1748470129.4287326, "batch_id": "batch_3", "batch_index": 2, "processing_mode": "sequential"}, {"request_id": "req_11", "context_hash": "5338010343620962780", "sequence_length": 923, "latency": 1.4664952754974365, "success": true, "timestamp": 1748470131.0854285, "batch_id": "batch_3", "batch_index": 2, "processing_mode": "sequential"}, {"request_id": "req_13", "context_hash": "2315025632649358788", "sequence_length": 766, "latency": 0.8658971786499023, "success": true, "timestamp": 1748470132.551952, "batch_id": "batch_3", "batch_index": 2, "processing_mode": "sequential"}, {"request_id": "req_12", "context_hash": "966278001392286027", "sequence_length": 793, "latency": 0.4125332832336426, "success": true, "timestamp": 1748470133.4178743, "batch_id": "batch_3", "batch_index": 2, "processing_mode": "sequential"}]}
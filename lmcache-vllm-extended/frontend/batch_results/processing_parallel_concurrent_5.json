{"experiment_config": {"num_batches": 5, "batch_size": 15, "repeat_ratio": 0.4, "diversity_level": 0.7, "processing_mode": "parallel", "max_concurrent": 5}, "metrics": {"total_requests": 75, "successful_requests": 75, "failed_requests": 0, "average_latency": 2.0142012151082356, "total_latency": 151.06509113311768, "throughput": 0.4964747277973734}, "detailed_results": [{"request_id": "req_8", "context_hash": "-3131317143975906740", "sequence_length": 666, "latency": 1.8504588603973389, "success": true, "timestamp": 1748476768.881309, "batch_id": "batch_1", "batch_index": 0, "processing_mode": "parallel"}, {"request_id": "req_14", "context_hash": "-3131317143975906740", "sequence_length": 668, "latency": 1.8497426509857178, "success": true, "timestamp": 1748476768.8820672, "batch_id": "batch_1", "batch_index": 0, "processing_mode": "parallel"}, {"request_id": "req_13", "context_hash": "5915430191113080886", "sequence_length": 702, "latency": 1.8495051860809326, "success": true, "timestamp": 1748476768.8822045, "batch_id": "batch_1", "batch_index": 0, "processing_mode": "parallel"}, {"request_id": "req_9", "context_hash": "5915430191113080886", "sequence_length": 703, "latency": 1.8495779037475586, "success": true, "timestamp": 1748476768.8822725, "batch_id": "batch_1", "batch_index": 0, "processing_mode": "parallel"}, {"request_id": "req_3", "context_hash": "-3196874742610025414", "sequence_length": 764, "latency": 1.8489031791687012, "success": true, "timestamp": 1748476768.882329, "batch_id": "batch_1", "batch_index": 0, "processing_mode": "parallel"}, {"request_id": "req_10", "context_hash": "9055770437960314596", "sequence_length": 885, "latency": 1.804792881011963, "success": true, "timestamp": 1748476770.7313986, "batch_id": "batch_1", "batch_index": 0, "processing_mode": "parallel"}, {"request_id": "req_6", "context_hash": "9055770437960314596", "sequence_length": 885, "latency": 1.8046135902404785, "success": true, "timestamp": 1748476770.7318702, "batch_id": "batch_1", "batch_index": 0, "processing_mode": "parallel"}, {"request_id": "req_5", "context_hash": "-7247488039259393197", "sequence_length": 892, "latency": 1.8045191764831543, "success": true, "timestamp": 1748476770.732008, "batch_id": "batch_1", "batch_index": 0, "processing_mode": "parallel"}, {"request_id": "req_11", "context_hash": "-7247488039259393197", "sequence_length": 893, "latency": 1.8044545650482178, "success": true, "timestamp": 1748476770.7321053, "batch_id": "batch_1", "batch_index": 0, "processing_mode": "parallel"}, {"request_id": "req_7", "context_hash": "-1316916962358048013", "sequence_length": 905, "latency": 1.8044013977050781, "success": true, "timestamp": 1748476770.7321918, "batch_id": "batch_1", "batch_index": 0, "processing_mode": "parallel"}, {"request_id": "req_4", "context_hash": "-3105217576941411677", "sequence_length": 912, "latency": 3.00907826423645, "success": true, "timestamp": 1748476772.536274, "batch_id": "batch_1", "batch_index": 0, "processing_mode": "parallel"}, {"request_id": "req_2", "context_hash": "-3991654701869045138", "sequence_length": 923, "latency": 3.0088071823120117, "success": true, "timestamp": 1748476772.5366092, "batch_id": "batch_1", "batch_index": 0, "processing_mode": "parallel"}, {"request_id": "req_15", "context_hash": "3413906998889812395", "sequence_length": 20999, "latency": 3.6393277645111084, "success": true, "timestamp": 1748476772.5367267, "batch_id": "batch_1", "batch_index": 0, "processing_mode": "parallel"}, {"request_id": "req_12", "context_hash": "3413906998889812395", "sequence_length": 21000, "latency": 2.877807855606079, "success": true, "timestamp": 1748476772.5371165, "batch_id": "batch_1", "batch_index": 0, "processing_mode": "parallel"}, {"request_id": "req_1", "context_hash": "3413906998889812395", "sequence_length": 21001, "latency": 1.3073091506958008, "success": true, "timestamp": 1748476772.5374556, "batch_id": "batch_1", "batch_index": 0, "processing_mode": "parallel"}, {"request_id": "req_26", "context_hash": "-3105217576941411677", "sequence_length": 909, "latency": 1.8084626197814941, "success": true, "timestamp": 1748476776.1767757, "batch_id": "batch_2", "batch_index": 1, "processing_mode": "parallel"}, {"request_id": "req_20", "context_hash": "-3105217576941411677", "sequence_length": 910, "latency": 1.8086225986480713, "success": true, "timestamp": 1748476776.1769807, "batch_id": "batch_2", "batch_index": 1, "processing_mode": "parallel"}, {"request_id": "req_27", "context_hash": "-3105217576941411677", "sequence_length": 911, "latency": 1.8086411952972412, "success": true, "timestamp": 1748476776.177051, "batch_id": "batch_2", "batch_index": 1, "processing_mode": "parallel"}, {"request_id": "req_25", "context_hash": "-1316916962358048013", "sequence_length": 904, "latency": 1.8085477352142334, "success": true, "timestamp": 1748476776.177105, "batch_id": "batch_2", "batch_index": 1, "processing_mode": "parallel"}, {"request_id": "req_17", "context_hash": "-1316916962358048013", "sequence_length": 904, "latency": 1.8085699081420898, "success": true, "timestamp": 1748476776.1771607, "batch_id": "batch_2", "batch_index": 1, "processing_mode": "parallel"}, {"request_id": "req_28", "context_hash": "-1316916962358048013", "sequence_length": 905, "latency": 2.8238577842712402, "success": true, "timestamp": 1748476777.9853368, "batch_id": "batch_2", "batch_index": 1, "processing_mode": "parallel"}, {"request_id": "req_22", "context_hash": "3413906998889812395", "sequence_length": 20999, "latency": 3.417936086654663, "success": true, "timestamp": 1748476777.985749, "batch_id": "batch_2", "batch_index": 1, "processing_mode": "parallel"}, {"request_id": "req_30", "context_hash": "3413906998889812395", "sequence_length": 20999, "latency": 1.6892831325531006, "success": true, "timestamp": 1748476777.986167, "batch_id": "batch_2", "batch_index": 1, "processing_mode": "parallel"}, {"request_id": "req_29", "context_hash": "3413906998889812395", "sequence_length": 21001, "latency": 1.3730740547180176, "success": true, "timestamp": 1748476777.9865284, "batch_id": "batch_2", "batch_index": 1, "processing_mode": "parallel"}, {"request_id": "req_23", "context_hash": "-3131317143975906740", "sequence_length": 669, "latency": 2.8226263523101807, "success": true, "timestamp": 1748476777.9868717, "batch_id": "batch_2", "batch_index": 1, "processing_mode": "parallel"}, {"request_id": "req_19", "context_hash": "9055770437960314596", "sequence_length": 885, "latency": 2.2755308151245117, "success": true, "timestamp": 1748476779.359638, "batch_id": "batch_2", "batch_index": 1, "processing_mode": "parallel"}, {"request_id": "req_18", "context_hash": "6908810260804244352", "sequence_length": 610, "latency": 2.1449103355407715, "success": true, "timestamp": 1748476779.67548, "batch_id": "batch_2", "batch_index": 1, "processing_mode": "parallel"}, {"request_id": "req_16", "context_hash": "-4717087325188037796", "sequence_length": 666, "latency": 1.8638818264007568, "success": true, "timestamp": 1748476780.8092496, "batch_id": "batch_2", "batch_index": 1, "processing_mode": "parallel"}, {"request_id": "req_21", "context_hash": "-1586337643667903370", "sequence_length": 679, "latency": 1.8636972904205322, "success": true, "timestamp": 1748476780.8095202, "batch_id": "batch_2", "batch_index": 1, "processing_mode": "parallel"}, {"request_id": "req_24", "context_hash": "-4306125032317618726", "sequence_length": 790, "latency": 1.6887881755828857, "success": true, "timestamp": 1748476781.403715, "batch_id": "batch_2", "batch_index": 1, "processing_mode": "parallel"}, {"request_id": "req_40", "context_hash": "-3196874742610025414", "sequence_length": 765, "latency": 1.7840676307678223, "success": true, "timestamp": 1748476783.0930183, "batch_id": "batch_3", "batch_index": 2, "processing_mode": "parallel"}, {"request_id": "req_35", "context_hash": "-3196874742610025414", "sequence_length": 766, "latency": 1.7839627265930176, "success": true, "timestamp": 1748476783.0932083, "batch_id": "batch_3", "batch_index": 2, "processing_mode": "parallel"}, {"request_id": "req_42", "context_hash": "-3196874742610025414", "sequence_length": 766, "latency": 1.7838540077209473, "success": true, "timestamp": 1748476783.0932796, "batch_id": "batch_3", "batch_index": 2, "processing_mode": "parallel"}, {"request_id": "req_44", "context_hash": "-3131317143975906740", "sequence_length": 669, "latency": 1.7833895683288574, "success": true, "timestamp": 1748476783.0933363, "batch_id": "batch_3", "batch_index": 2, "processing_mode": "parallel"}, {"request_id": "req_34", "context_hash": "-3131317143975906740", "sequence_length": 669, "latency": 1.783815622329712, "success": true, "timestamp": 1748476783.0933895, "batch_id": "batch_3", "batch_index": 2, "processing_mode": "parallel"}, {"request_id": "req_36", "context_hash": "9055770437960314596", "sequence_length": 882, "latency": 1.7919056415557861, "success": true, "timestamp": 1748476784.876822, "batch_id": "batch_3", "batch_index": 2, "processing_mode": "parallel"}, {"request_id": "req_43", "context_hash": "9055770437960314596", "sequence_length": 883, "latency": 1.7918245792388916, "success": true, "timestamp": 1748476784.8772233, "batch_id": "batch_3", "batch_index": 2, "processing_mode": "parallel"}, {"request_id": "req_41", "context_hash": "5915430191113080886", "sequence_length": 700, "latency": 1.7917511463165283, "success": true, "timestamp": 1748476784.8773425, "batch_id": "batch_3", "batch_index": 2, "processing_mode": "parallel"}, {"request_id": "req_38", "context_hash": "5915430191113080886", "sequence_length": 702, "latency": 1.791698694229126, "success": true, "timestamp": 1748476784.877432, "batch_id": "batch_3", "batch_index": 2, "processing_mode": "parallel"}, {"request_id": "req_32", "context_hash": "-1586337643667903370", "sequence_length": 680, "latency": 1.7916524410247803, "success": true, "timestamp": 1748476784.8775105, "batch_id": "batch_3", "batch_index": 2, "processing_mode": "parallel"}, {"request_id": "req_45", "context_hash": "-1586337643667903370", "sequence_length": 682, "latency": 2.4949564933776855, "success": true, "timestamp": 1748476786.6688142, "batch_id": "batch_3", "batch_index": 2, "processing_mode": "parallel"}, {"request_id": "req_37", "context_hash": "3413906998889812395", "sequence_length": 21001, "latency": 2.515261650085449, "success": true, "timestamp": 1748476786.6691797, "batch_id": "batch_3", "batch_index": 2, "processing_mode": "parallel"}, {"request_id": "req_31", "context_hash": "-3105217576941411677", "sequence_length": 910, "latency": 2.4941132068634033, "success": true, "timestamp": 1748476786.6695828, "batch_id": "batch_3", "batch_index": 2, "processing_mode": "parallel"}, {"request_id": "req_39", "context_hash": "-4306125032317618726", "sequence_length": 790, "latency": 2.494046926498413, "success": true, "timestamp": 1748476786.6696885, "batch_id": "batch_3", "batch_index": 2, "processing_mode": "parallel"}, {"request_id": "req_33", "context_hash": "807901750979960038", "sequence_length": 810, "latency": 2.493802070617676, "success": true, "timestamp": 1748476786.6697717, "batch_id": "batch_3", "batch_index": 2, "processing_mode": "parallel"}, {"request_id": "req_58", "context_hash": "-3991654701869045138", "sequence_length": 922, "latency": 1.7960150241851807, "success": true, "timestamp": 1748476789.1849396, "batch_id": "batch_4", "batch_index": 3, "processing_mode": "parallel"}, {"request_id": "req_49", "context_hash": "-3991654701869045138", "sequence_length": 923, "latency": 1.7957820892333984, "success": true, "timestamp": 1748476789.1851354, "batch_id": "batch_4", "batch_index": 3, "processing_mode": "parallel"}, {"request_id": "req_59", "context_hash": "-3991654701869045138", "sequence_length": 925, "latency": 1.7956688404083252, "success": true, "timestamp": 1748476789.1852036, "batch_id": "batch_4", "batch_index": 3, "processing_mode": "parallel"}, {"request_id": "req_51", "context_hash": "-1586337643667903370", "sequence_length": 680, "latency": 1.7952792644500732, "success": true, "timestamp": 1748476789.1852605, "batch_id": "batch_4", "batch_index": 3, "processing_mode": "parallel"}, {"request_id": "req_56", "context_hash": "-1586337643667903370", "sequence_length": 681, "latency": 1.7956745624542236, "success": true, "timestamp": 1748476789.1853144, "batch_id": "batch_4", "batch_index": 3, "processing_mode": "parallel"}, {"request_id": "req_57", "context_hash": "-1586337643667903370", "sequence_length": 682, "latency": 1.7910926342010498, "success": true, "timestamp": 1748476790.9806292, "batch_id": "batch_4", "batch_index": 3, "processing_mode": "parallel"}, {"request_id": "req_60", "context_hash": "-3196874742610025414", "sequence_length": 765, "latency": 1.7910051345825195, "success": true, "timestamp": 1748476790.9810045, "batch_id": "batch_4", "batch_index": 3, "processing_mode": "parallel"}, {"request_id": "req_50", "context_hash": "-3196874742610025414", "sequence_length": 766, "latency": 1.7909290790557861, "success": true, "timestamp": 1748476790.9811206, "batch_id": "batch_4", "batch_index": 3, "processing_mode": "parallel"}, {"request_id": "req_46", "context_hash": "-3105217576941411677", "sequence_length": 911, "latency": 1.79087233543396, "success": true, "timestamp": 1748476790.9812114, "batch_id": "batch_4", "batch_index": 3, "processing_mode": "parallel"}, {"request_id": "req_55", "context_hash": "-3105217576941411677", "sequence_length": 912, "latency": 1.7908177375793457, "success": true, "timestamp": 1748476790.9812973, "batch_id": "batch_4", "batch_index": 3, "processing_mode": "parallel"}, {"request_id": "req_48", "context_hash": "9055770437960314596", "sequence_length": 883, "latency": 2.5201621055603027, "success": true, "timestamp": 1748476792.7718031, "batch_id": "batch_4", "batch_index": 3, "processing_mode": "parallel"}, {"request_id": "req_54", "context_hash": "3413906998889812395", "sequence_length": 21000, "latency": 2.5199673175811768, "success": true, "timestamp": 1748476792.7721295, "batch_id": "batch_4", "batch_index": 3, "processing_mode": "parallel"}, {"request_id": "req_53", "context_hash": "-3131317143975906740", "sequence_length": 669, "latency": 2.5196146965026855, "success": true, "timestamp": 1748476792.7725248, "batch_id": "batch_4", "batch_index": 3, "processing_mode": "parallel"}, {"request_id": "req_52", "context_hash": "5915430191113080886", "sequence_length": 703, "latency": 2.519540786743164, "success": true, "timestamp": 1748476792.7726324, "batch_id": "batch_4", "batch_index": 3, "processing_mode": "parallel"}, {"request_id": "req_47", "context_hash": "-7645560756092944718", "sequence_length": 832, "latency": 2.51948881149292, "success": true, "timestamp": 1748476792.7727163, "batch_id": "batch_4", "batch_index": 3, "processing_mode": "parallel"}, {"request_id": "req_72", "context_hash": "-1316916962358048013", "sequence_length": 905, "latency": 1.8175511360168457, "success": true, "timestamp": 1748476795.2926185, "batch_id": "batch_5", "batch_index": 4, "processing_mode": "parallel"}, {"request_id": "req_75", "context_hash": "-1316916962358048013", "sequence_length": 905, "latency": 1.817803144454956, "success": true, "timestamp": 1748476795.2928057, "batch_id": "batch_5", "batch_index": 4, "processing_mode": "parallel"}, {"request_id": "req_62", "context_hash": "-1316916962358048013", "sequence_length": 906, "latency": 1.8176968097686768, "success": true, "timestamp": 1748476795.292876, "batch_id": "batch_5", "batch_index": 4, "processing_mode": "parallel"}, {"request_id": "req_74", "context_hash": "-3196874742610025414", "sequence_length": 764, "latency": 1.8177103996276855, "success": true, "timestamp": 1748476795.2929323, "batch_id": "batch_5", "batch_index": 4, "processing_mode": "parallel"}, {"request_id": "req_71", "context_hash": "-3196874742610025414", "sequence_length": 766, "latency": 1.817542552947998, "success": true, "timestamp": 1748476795.2929845, "batch_id": "batch_5", "batch_index": 4, "processing_mode": "parallel"}, {"request_id": "req_69", "context_hash": "-3196874742610025414", "sequence_length": 766, "latency": 1.7976484298706055, "success": true, "timestamp": 1748476797.1102688, "batch_id": "batch_5", "batch_index": 4, "processing_mode": "parallel"}, {"request_id": "req_61", "context_hash": "-3991654701869045138", "sequence_length": 923, "latency": 1.797583818435669, "success": true, "timestamp": 1748476797.11066, "batch_id": "batch_5", "batch_index": 4, "processing_mode": "parallel"}, {"request_id": "req_70", "context_hash": "-3991654701869045138", "sequence_length": 924, "latency": 1.7975091934204102, "success": true, "timestamp": 1748476797.1107812, "batch_id": "batch_5", "batch_index": 4, "processing_mode": "parallel"}, {"request_id": "req_63", "context_hash": "-4717087325188037796", "sequence_length": 664, "latency": 1.7974541187286377, "success": true, "timestamp": 1748476797.1108725, "batch_id": "batch_5", "batch_index": 4, "processing_mode": "parallel"}, {"request_id": "req_73", "context_hash": "-4717087325188037796", "sequence_length": 666, "latency": 1.797403335571289, "success": true, "timestamp": 1748476797.110955, "batch_id": "batch_5", "batch_index": 4, "processing_mode": "parallel"}, {"request_id": "req_66", "context_hash": "807901750979960038", "sequence_length": 807, "latency": 1.7937676906585693, "success": true, "timestamp": 1748476798.9080062, "batch_id": "batch_5", "batch_index": 4, "processing_mode": "parallel"}, {"request_id": "req_67", "context_hash": "-1586337643667903370", "sequence_length": 680, "latency": 1.7935166358947754, "success": true, "timestamp": 1748476798.9083745, "batch_id": "batch_5", "batch_index": 4, "processing_mode": "parallel"}, {"request_id": "req_64", "context_hash": "9055770437960314596", "sequence_length": 885, "latency": 1.793440580368042, "success": true, "timestamp": 1748476798.908488, "batch_id": "batch_5", "batch_index": 4, "processing_mode": "parallel"}, {"request_id": "req_65", "context_hash": "-3131317143975906740", "sequence_length": 667, "latency": 1.7933826446533203, "success": true, "timestamp": 1748476798.9085798, "batch_id": "batch_5", "batch_index": 4, "processing_mode": "parallel"}, {"request_id": "req_68", "context_hash": "-3105217576941411677", "sequence_length": 912, "latency": 1.7933382987976074, "success": true, "timestamp": 1748476798.9086597, "batch_id": "batch_5", "batch_index": 4, "processing_mode": "parallel"}]}
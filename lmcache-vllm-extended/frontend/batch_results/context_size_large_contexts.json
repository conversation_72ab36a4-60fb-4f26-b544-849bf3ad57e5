{"experiment_config": {"num_batches": 5, "batch_size": 10, "repeat_ratio": 0.2, "diversity_level": 1.0, "processing_mode": "sequential", "max_concurrent": null}, "metrics": {"total_requests": 50, "successful_requests": 50, "failed_requests": 0, "average_latency": 1.739299077987671, "total_latency": 86.96495389938354, "throughput": 0.5749442477466136}, "detailed_results": [{"request_id": "req_2", "context_hash": "-3392439222571228596", "sequence_length": 607, "latency": 1.672149419784546, "success": true, "timestamp": 1748476486.2145753, "batch_id": "batch_1", "batch_index": 0, "processing_mode": "sequential"}, {"request_id": "req_7", "context_hash": "-8506777032458791370", "sequence_length": 700, "latency": 1.637538194656372, "success": true, "timestamp": 1748476487.886797, "batch_id": "batch_1", "batch_index": 0, "processing_mode": "sequential"}, {"request_id": "req_10", "context_hash": "3789283109748548514", "sequence_length": 764, "latency": 1.6383991241455078, "success": true, "timestamp": 1748476489.52437, "batch_id": "batch_1", "batch_index": 0, "processing_mode": "sequential"}, {"request_id": "req_8", "context_hash": "3789283109748548514", "sequence_length": 765, "latency": 1.6385271549224854, "success": true, "timestamp": 1748476491.1627953, "batch_id": "batch_1", "batch_index": 0, "processing_mode": "sequential"}, {"request_id": "req_9", "context_hash": "3789283109748548514", "sequence_length": 766, "latency": 1.640324354171753, "success": true, "timestamp": 1748476492.8013456, "batch_id": "batch_1", "batch_index": 0, "processing_mode": "sequential"}, {"request_id": "req_5", "context_hash": "-3503223695531315344", "sequence_length": 792, "latency": 1.6412978172302246, "success": true, "timestamp": 1748476494.4416916, "batch_id": "batch_1", "batch_index": 0, "processing_mode": "sequential"}, {"request_id": "req_4", "context_hash": "-3410368058697351822", "sequence_length": 808, "latency": 1.6424319744110107, "success": true, "timestamp": 1748476496.0830107, "batch_id": "batch_1", "batch_index": 0, "processing_mode": "sequential"}, {"request_id": "req_3", "context_hash": "6938339784834599929", "sequence_length": 885, "latency": 1.645402193069458, "success": true, "timestamp": 1748476497.7254636, "batch_id": "batch_1", "batch_index": 0, "processing_mode": "sequential"}, {"request_id": "req_6", "context_hash": "-1250078326412131611", "sequence_length": 907, "latency": 1.6466970443725586, "success": true, "timestamp": 1748476499.3708897, "batch_id": "batch_1", "batch_index": 0, "processing_mode": "sequential"}, {"request_id": "req_1", "context_hash": "-2599458089772716135", "sequence_length": 21001, "latency": 2.3232645988464355, "success": true, "timestamp": 1748476501.0176122, "batch_id": "batch_1", "batch_index": 0, "processing_mode": "sequential"}, {"request_id": "req_20", "context_hash": "-2599458089772716135", "sequence_length": 20999, "latency": 2.3157079219818115, "success": true, "timestamp": 1748476503.3414097, "batch_id": "batch_2", "batch_index": 1, "processing_mode": "sequential"}, {"request_id": "req_14", "context_hash": "-2599458089772716135", "sequence_length": 21001, "latency": 2.3370249271392822, "success": true, "timestamp": 1748476505.6571503, "batch_id": "batch_2", "batch_index": 1, "processing_mode": "sequential"}, {"request_id": "req_19", "context_hash": "-1250078326412131611", "sequence_length": 905, "latency": 1.639383316040039, "success": true, "timestamp": 1748476507.9942107, "batch_id": "batch_2", "batch_index": 1, "processing_mode": "sequential"}, {"request_id": "req_13", "context_hash": "-1250078326412131611", "sequence_length": 905, "latency": 1.645420789718628, "success": true, "timestamp": 1748476509.6336265, "batch_id": "batch_2", "batch_index": 1, "processing_mode": "sequential"}, {"request_id": "req_18", "context_hash": "-8506777032458791370", "sequence_length": 700, "latency": 1.6387531757354736, "success": true, "timestamp": 1748476511.2790775, "batch_id": "batch_2", "batch_index": 1, "processing_mode": "sequential"}, {"request_id": "req_16", "context_hash": "-3410368058697351822", "sequence_length": 807, "latency": 1.6425652503967285, "success": true, "timestamp": 1748476512.917858, "batch_id": "batch_2", "batch_index": 1, "processing_mode": "sequential"}, {"request_id": "req_15", "context_hash": "-3392439222571228596", "sequence_length": 607, "latency": 1.6371300220489502, "success": true, "timestamp": 1748476514.5604484, "batch_id": "batch_2", "batch_index": 1, "processing_mode": "sequential"}, {"request_id": "req_12", "context_hash": "1031095652928343826", "sequence_length": 669, "latency": 1.641397476196289, "success": true, "timestamp": 1748476516.197604, "batch_id": "batch_2", "batch_index": 1, "processing_mode": "sequential"}, {"request_id": "req_11", "context_hash": "-2934992377117707600", "sequence_length": 682, "latency": 1.63978910446167, "success": true, "timestamp": 1748476517.8390255, "batch_id": "batch_2", "batch_index": 1, "processing_mode": "sequential"}, {"request_id": "req_17", "context_hash": "-5673968181701729554", "sequence_length": 832, "latency": 1.641685962677002, "success": true, "timestamp": 1748476519.47884, "batch_id": "batch_2", "batch_index": 1, "processing_mode": "sequential"}, {"request_id": "req_23", "context_hash": "-2599458089772716135", "sequence_length": 21000, "latency": 2.328732490539551, "success": true, "timestamp": 1748476521.1208386, "batch_id": "batch_3", "batch_index": 2, "processing_mode": "sequential"}, {"request_id": "req_30", "context_hash": "-2599458089772716135", "sequence_length": 21000, "latency": 2.3577325344085693, "success": true, "timestamp": 1748476523.4496026, "batch_id": "batch_3", "batch_index": 2, "processing_mode": "sequential"}, {"request_id": "req_29", "context_hash": "-1250078326412131611", "sequence_length": 904, "latency": 1.6423466205596924, "success": true, "timestamp": 1748476525.8073683, "batch_id": "batch_3", "batch_index": 2, "processing_mode": "sequential"}, {"request_id": "req_28", "context_hash": "-1250078326412131611", "sequence_length": 907, "latency": 1.6468191146850586, "success": true, "timestamp": 1748476527.4497442, "batch_id": "batch_3", "batch_index": 2, "processing_mode": "sequential"}, {"request_id": "req_25", "context_hash": "-3503223695531315344", "sequence_length": 792, "latency": 1.6445598602294922, "success": true, "timestamp": 1748476529.0965896, "batch_id": "batch_3", "batch_index": 2, "processing_mode": "sequential"}, {"request_id": "req_22", "context_hash": "1031095652928343826", "sequence_length": 667, "latency": 1.6421222686767578, "success": true, "timestamp": 1748476530.7411726, "batch_id": "batch_3", "batch_index": 2, "processing_mode": "sequential"}, {"request_id": "req_27", "context_hash": "-2934992377117707600", "sequence_length": 681, "latency": 1.6431260108947754, "success": true, "timestamp": 1748476532.3833194, "batch_id": "batch_3", "batch_index": 2, "processing_mode": "sequential"}, {"request_id": "req_26", "context_hash": "-5673968181701729554", "sequence_length": 829, "latency": 1.643350601196289, "success": true, "timestamp": 1748476534.0264697, "batch_id": "batch_3", "batch_index": 2, "processing_mode": "sequential"}, {"request_id": "req_21", "context_hash": "6938339784834599929", "sequence_length": 883, "latency": 1.6452126502990723, "success": true, "timestamp": 1748476535.6698427, "batch_id": "batch_3", "batch_index": 2, "processing_mode": "sequential"}, {"request_id": "req_24", "context_hash": "1692588648391791478", "sequence_length": 891, "latency": 1.6475536823272705, "success": true, "timestamp": 1748476537.3150775, "batch_id": "batch_3", "batch_index": 2, "processing_mode": "sequential"}, {"request_id": "req_40", "context_hash": "-3392439222571228596", "sequence_length": 610, "latency": 1.6393375396728516, "success": true, "timestamp": 1748476538.9629135, "batch_id": "batch_4", "batch_index": 3, "processing_mode": "sequential"}, {"request_id": "req_31", "context_hash": "-3392439222571228596", "sequence_length": 610, "latency": 1.6403324604034424, "success": true, "timestamp": 1748476540.6022868, "batch_id": "batch_4", "batch_index": 3, "processing_mode": "sequential"}, {"request_id": "req_33", "context_hash": "-2934992377117707600", "sequence_length": 682, "latency": 1.6418070793151855, "success": true, "timestamp": 1748476542.2426589, "batch_id": "batch_4", "batch_index": 3, "processing_mode": "sequential"}, {"request_id": "req_32", "context_hash": "-2599458089772716135", "sequence_length": 20998, "latency": 2.3182032108306885, "success": true, "timestamp": 1748476543.8844974, "batch_id": "batch_4", "batch_index": 3, "processing_mode": "sequential"}, {"request_id": "req_36", "context_hash": "1692588648391791478", "sequence_length": 894, "latency": 1.6414201259613037, "success": true, "timestamp": 1748476546.2027404, "batch_id": "batch_4", "batch_index": 3, "processing_mode": "sequential"}, {"request_id": "req_38", "context_hash": "-8506777032458791370", "sequence_length": 703, "latency": 1.6427085399627686, "success": true, "timestamp": 1748476547.8441963, "batch_id": "batch_4", "batch_index": 3, "processing_mode": "sequential"}, {"request_id": "req_35", "context_hash": "-3503223695531315344", "sequence_length": 791, "latency": 1.642798900604248, "success": true, "timestamp": 1748476549.4869392, "batch_id": "batch_4", "batch_index": 3, "processing_mode": "sequential"}, {"request_id": "req_37", "context_hash": "9105612739312739234", "sequence_length": 666, "latency": 1.639479160308838, "success": true, "timestamp": 1748476551.1297781, "batch_id": "batch_4", "batch_index": 3, "processing_mode": "sequential"}, {"request_id": "req_34", "context_hash": "-5080178105664786127", "sequence_length": 910, "latency": 1.6460092067718506, "success": true, "timestamp": 1748476552.7692933, "batch_id": "batch_4", "batch_index": 3, "processing_mode": "sequential"}, {"request_id": "req_39", "context_hash": "-5080178105664786127", "sequence_length": 912, "latency": 1.6465840339660645, "success": true, "timestamp": 1748476554.4153454, "batch_id": "batch_4", "batch_index": 3, "processing_mode": "sequential"}, {"request_id": "req_41", "context_hash": "-8506777032458791370", "sequence_length": 700, "latency": 1.6416382789611816, "success": true, "timestamp": 1748476556.0622103, "batch_id": "batch_5", "batch_index": 4, "processing_mode": "sequential"}, {"request_id": "req_49", "context_hash": "-8506777032458791370", "sequence_length": 703, "latency": 1.643599510192871, "success": true, "timestamp": 1748476557.7038877, "batch_id": "batch_5", "batch_index": 4, "processing_mode": "sequential"}, {"request_id": "req_44", "context_hash": "-5080178105664786127", "sequence_length": 909, "latency": 1.647068977355957, "success": true, "timestamp": 1748476559.3475182, "batch_id": "batch_5", "batch_index": 4, "processing_mode": "sequential"}, {"request_id": "req_50", "context_hash": "-5080178105664786127", "sequence_length": 911, "latency": 1.6478822231292725, "success": true, "timestamp": 1748476560.9946156, "batch_id": "batch_5", "batch_index": 4, "processing_mode": "sequential"}, {"request_id": "req_43", "context_hash": "-3503223695531315344", "sequence_length": 790, "latency": 1.6410725116729736, "success": true, "timestamp": 1748476562.6425226, "batch_id": "batch_5", "batch_index": 4, "processing_mode": "sequential"}, {"request_id": "req_45", "context_hash": "-5673968181701729554", "sequence_length": 832, "latency": 1.6417055130004883, "success": true, "timestamp": 1748476564.2836225, "batch_id": "batch_5", "batch_index": 4, "processing_mode": "sequential"}, {"request_id": "req_42", "context_hash": "-2934992377117707600", "sequence_length": 681, "latency": 1.6411950588226318, "success": true, "timestamp": 1748476565.9253528, "batch_id": "batch_5", "batch_index": 4, "processing_mode": "sequential"}, {"request_id": "req_47", "context_hash": "-2599458089772716135", "sequence_length": 21000, "latency": 2.3325257301330566, "success": true, "timestamp": 1748476567.5665736, "batch_id": "batch_5", "batch_index": 4, "processing_mode": "sequential"}, {"request_id": "req_48", "context_hash": "1692588648391791478", "sequence_length": 894, "latency": 1.6389946937561035, "success": true, "timestamp": 1748476569.8991325, "batch_id": "batch_5", "batch_index": 4, "processing_mode": "sequential"}, {"request_id": "req_46", "context_hash": "3789283109748548514", "sequence_length": 766, "latency": 1.6441454887390137, "success": true, "timestamp": 1748476571.5381572, "batch_id": "batch_5", "batch_index": 4, "processing_mode": "sequential"}]}
{"experiment_config": {"num_batches": 5, "batch_size": 15, "repeat_ratio": 0.4, "diversity_level": 0.7, "processing_mode": "parallel", "max_concurrent": 10}, "metrics": {"total_requests": 75, "successful_requests": 75, "failed_requests": 0, "average_latency": 2.277618096669515, "total_latency": 170.82135725021362, "throughput": 0.4390551697241371}, "detailed_results": [{"request_id": "req_1", "context_hash": "-6589667350013808819", "sequence_length": 610, "latency": 2.0966169834136963, "success": true, "timestamp": 1748476805.538493, "batch_id": "batch_1", "batch_index": 0, "processing_mode": "parallel"}, {"request_id": "req_13", "context_hash": "3575131124967853148", "sequence_length": 668, "latency": 2.095522165298462, "success": true, "timestamp": 1748476805.5394957, "batch_id": "batch_1", "batch_index": 0, "processing_mode": "parallel"}, {"request_id": "req_2", "context_hash": "3575131124967853148", "sequence_length": 669, "latency": 2.0954349040985107, "success": true, "timestamp": 1748476805.5396354, "batch_id": "batch_1", "batch_index": 0, "processing_mode": "parallel"}, {"request_id": "req_11", "context_hash": "3575131124967853148", "sequence_length": 669, "latency": 2.0955069065093994, "success": true, "timestamp": 1748476805.5397017, "batch_id": "batch_1", "batch_index": 0, "processing_mode": "parallel"}, {"request_id": "req_5", "context_hash": "-4429114490780365015", "sequence_length": 679, "latency": 2.0952205657958984, "success": true, "timestamp": 1748476805.5397568, "batch_id": "batch_1", "batch_index": 0, "processing_mode": "parallel"}, {"request_id": "req_6", "context_hash": "3591480436150793890", "sequence_length": 791, "latency": 2.0953309535980225, "success": true, "timestamp": 1748476805.5398147, "batch_id": "batch_1", "batch_index": 0, "processing_mode": "parallel"}, {"request_id": "req_7", "context_hash": "2496976890292295551", "sequence_length": 809, "latency": 2.095050096511841, "success": true, "timestamp": 1748476805.539869, "batch_id": "batch_1", "batch_index": 0, "processing_mode": "parallel"}, {"request_id": "req_10", "context_hash": "2496976890292295551", "sequence_length": 810, "latency": 2.095107316970825, "success": true, "timestamp": 1748476805.54007, "batch_id": "batch_1", "batch_index": 0, "processing_mode": "parallel"}, {"request_id": "req_15", "context_hash": "-5528456955917186401", "sequence_length": 885, "latency": 2.09511661529541, "success": true, "timestamp": 1748476805.5401232, "batch_id": "batch_1", "batch_index": 0, "processing_mode": "parallel"}, {"request_id": "req_4", "context_hash": "-5528456955917186401", "sequence_length": 885, "latency": 2.0941996574401855, "success": true, "timestamp": 1748476805.5401754, "batch_id": "batch_1", "batch_index": 0, "processing_mode": "parallel"}, {"request_id": "req_9", "context_hash": "8499014852662082233", "sequence_length": 910, "latency": 2.6474931240081787, "success": true, "timestamp": 1748476807.6346066, "batch_id": "batch_1", "batch_index": 0, "processing_mode": "parallel"}, {"request_id": "req_14", "context_hash": "8499014852662082233", "sequence_length": 912, "latency": 2.6469287872314453, "success": true, "timestamp": 1748476807.635265, "batch_id": "batch_1", "batch_index": 0, "processing_mode": "parallel"}, {"request_id": "req_8", "context_hash": "-9059276493678423812", "sequence_length": 925, "latency": 2.6468276977539062, "success": true, "timestamp": 1748476807.6354074, "batch_id": "batch_1", "batch_index": 0, "processing_mode": "parallel"}, {"request_id": "req_3", "context_hash": "-6763931301789973693", "sequence_length": 21001, "latency": 0.9743037223815918, "success": true, "timestamp": 1748476807.6355116, "batch_id": "batch_1", "batch_index": 0, "processing_mode": "parallel"}, {"request_id": "req_12", "context_hash": "-6763931301789973693", "sequence_length": 21001, "latency": 2.9082493782043457, "success": true, "timestamp": 1748476807.6359031, "batch_id": "batch_1", "batch_index": 0, "processing_mode": "parallel"}, {"request_id": "req_22", "context_hash": "-5528456955917186401", "sequence_length": 882, "latency": 2.068143844604492, "success": true, "timestamp": 1748476810.544943, "batch_id": "batch_2", "batch_index": 1, "processing_mode": "parallel"}, {"request_id": "req_25", "context_hash": "-5528456955917186401", "sequence_length": 883, "latency": 2.0684661865234375, "success": true, "timestamp": 1748476810.5451486, "batch_id": "batch_2", "batch_index": 1, "processing_mode": "parallel"}, {"request_id": "req_28", "context_hash": "-5528456955917186401", "sequence_length": 885, "latency": 2.068498373031616, "success": true, "timestamp": 1748476810.5452173, "batch_id": "batch_2", "batch_index": 1, "processing_mode": "parallel"}, {"request_id": "req_29", "context_hash": "3591480436150793890", "sequence_length": 792, "latency": 2.068260431289673, "success": true, "timestamp": 1748476810.5452716, "batch_id": "batch_2", "batch_index": 1, "processing_mode": "parallel"}, {"request_id": "req_18", "context_hash": "3591480436150793890", "sequence_length": 793, "latency": 2.068328619003296, "success": true, "timestamp": 1748476810.545323, "batch_id": "batch_2", "batch_index": 1, "processing_mode": "parallel"}, {"request_id": "req_30", "context_hash": "3575131124967853148", "sequence_length": 666, "latency": 2.0682027339935303, "success": true, "timestamp": 1748476810.545373, "batch_id": "batch_2", "batch_index": 1, "processing_mode": "parallel"}, {"request_id": "req_19", "context_hash": "3575131124967853148", "sequence_length": 667, "latency": 2.06835675239563, "success": true, "timestamp": 1748476810.5454228, "batch_id": "batch_2", "batch_index": 1, "processing_mode": "parallel"}, {"request_id": "req_24", "context_hash": "8499014852662082233", "sequence_length": 910, "latency": 2.0682129859924316, "success": true, "timestamp": 1748476810.5454717, "batch_id": "batch_2", "batch_index": 1, "processing_mode": "parallel"}, {"request_id": "req_26", "context_hash": "8499014852662082233", "sequence_length": 911, "latency": 2.0679616928100586, "success": true, "timestamp": 1748476810.5455205, "batch_id": "batch_2", "batch_index": 1, "processing_mode": "parallel"}, {"request_id": "req_20", "context_hash": "-9059276493678423812", "sequence_length": 923, "latency": 2.0681796073913574, "success": true, "timestamp": 1748476810.5455685, "batch_id": "batch_2", "batch_index": 1, "processing_mode": "parallel"}, {"request_id": "req_23", "context_hash": "-6589667350013808819", "sequence_length": 607, "latency": 1.8126964569091797, "success": true, "timestamp": 1748476812.613231, "batch_id": "batch_2", "batch_index": 1, "processing_mode": "parallel"}, {"request_id": "req_16", "context_hash": "7330408824934193355", "sequence_length": 702, "latency": 1.8118934631347656, "success": true, "timestamp": 1748476812.613797, "batch_id": "batch_2", "batch_index": 1, "processing_mode": "parallel"}, {"request_id": "req_21", "context_hash": "2270151118685661352", "sequence_length": 832, "latency": 1.811887264251709, "success": true, "timestamp": 1748476812.6139262, "batch_id": "batch_2", "batch_index": 1, "processing_mode": "parallel"}, {"request_id": "req_27", "context_hash": "2270151118685661352", "sequence_length": 832, "latency": 1.811826229095459, "success": true, "timestamp": 1748476812.614026, "batch_id": "batch_2", "batch_index": 1, "processing_mode": "parallel"}, {"request_id": "req_17", "context_hash": "7434780898762791277", "sequence_length": 907, "latency": 1.811774730682373, "success": true, "timestamp": 1748476812.614112, "batch_id": "batch_2", "batch_index": 1, "processing_mode": "parallel"}, {"request_id": "req_35", "context_hash": "2270151118685661352", "sequence_length": 829, "latency": 2.055121421813965, "success": true, "timestamp": 1748476814.4264822, "batch_id": "batch_3", "batch_index": 2, "processing_mode": "parallel"}, {"request_id": "req_44", "context_hash": "2270151118685661352", "sequence_length": 831, "latency": 2.054831027984619, "success": true, "timestamp": 1748476814.426653, "batch_id": "batch_3", "batch_index": 2, "processing_mode": "parallel"}, {"request_id": "req_42", "context_hash": "2270151118685661352", "sequence_length": 832, "latency": 2.05484676361084, "success": true, "timestamp": 1748476814.4267194, "batch_id": "batch_3", "batch_index": 2, "processing_mode": "parallel"}, {"request_id": "req_32", "context_hash": "-5528456955917186401", "sequence_length": 885, "latency": 2.0548954010009766, "success": true, "timestamp": 1748476814.4267747, "batch_id": "batch_3", "batch_index": 2, "processing_mode": "parallel"}, {"request_id": "req_40", "context_hash": "-5528456955917186401", "sequence_length": 885, "latency": 2.0546185970306396, "success": true, "timestamp": 1748476814.426827, "batch_id": "batch_3", "batch_index": 2, "processing_mode": "parallel"}, {"request_id": "req_39", "context_hash": "-9059276493678423812", "sequence_length": 922, "latency": 2.05452036857605, "success": true, "timestamp": 1748476814.4268775, "batch_id": "batch_3", "batch_index": 2, "processing_mode": "parallel"}, {"request_id": "req_45", "context_hash": "-9059276493678423812", "sequence_length": 924, "latency": 2.054065227508545, "success": true, "timestamp": 1748476814.4269292, "batch_id": "batch_3", "batch_index": 2, "processing_mode": "parallel"}, {"request_id": "req_41", "context_hash": "3575131124967853148", "sequence_length": 668, "latency": 2.0545427799224854, "success": true, "timestamp": 1748476814.4269803, "batch_id": "batch_3", "batch_index": 2, "processing_mode": "parallel"}, {"request_id": "req_33", "context_hash": "3575131124967853148", "sequence_length": 669, "latency": 2.054675817489624, "success": true, "timestamp": 1748476814.4270277, "batch_id": "batch_3", "batch_index": 2, "processing_mode": "parallel"}, {"request_id": "req_34", "context_hash": "-4429114490780365015", "sequence_length": 679, "latency": 2.054563522338867, "success": true, "timestamp": 1748476814.4270735, "batch_id": "batch_3", "batch_index": 2, "processing_mode": "parallel"}, {"request_id": "req_31", "context_hash": "7434780898762791277", "sequence_length": 905, "latency": 1.80094313621521, "success": true, "timestamp": 1748476816.4811478, "batch_id": "batch_3", "batch_index": 2, "processing_mode": "parallel"}, {"request_id": "req_37", "context_hash": "8499014852662082233", "sequence_length": 911, "latency": 1.8004896640777588, "success": true, "timestamp": 1748476816.48172, "batch_id": "batch_3", "batch_index": 2, "processing_mode": "parallel"}, {"request_id": "req_36", "context_hash": "3341013862577558963", "sequence_length": 665, "latency": 1.8004004955291748, "success": true, "timestamp": 1748476816.4818506, "batch_id": "batch_3", "batch_index": 2, "processing_mode": "parallel"}, {"request_id": "req_43", "context_hash": "3039962379211266948", "sequence_length": 763, "latency": 1.8003356456756592, "success": true, "timestamp": 1748476816.4819493, "batch_id": "batch_3", "batch_index": 2, "processing_mode": "parallel"}, {"request_id": "req_38", "context_hash": "3039962379211266948", "sequence_length": 766, "latency": 1.8002798557281494, "success": true, "timestamp": 1748476816.4820359, "batch_id": "batch_3", "batch_index": 2, "processing_mode": "parallel"}, {"request_id": "req_51", "context_hash": "3341013862577558963", "sequence_length": 664, "latency": 2.0298385620117188, "success": true, "timestamp": 1748476818.2828557, "batch_id": "batch_4", "batch_index": 3, "processing_mode": "parallel"}, {"request_id": "req_55", "context_hash": "3341013862577558963", "sequence_length": 665, "latency": 2.029573678970337, "success": true, "timestamp": 1748476818.283021, "batch_id": "batch_4", "batch_index": 3, "processing_mode": "parallel"}, {"request_id": "req_58", "context_hash": "3341013862577558963", "sequence_length": 666, "latency": 2.0294723510742188, "success": true, "timestamp": 1748476818.283085, "batch_id": "batch_4", "batch_index": 3, "processing_mode": "parallel"}, {"request_id": "req_60", "context_hash": "-5528456955917186401", "sequence_length": 883, "latency": 2.029588222503662, "success": true, "timestamp": 1748476818.2831385, "batch_id": "batch_4", "batch_index": 3, "processing_mode": "parallel"}, {"request_id": "req_53", "context_hash": "-5528456955917186401", "sequence_length": 884, "latency": 2.0292394161224365, "success": true, "timestamp": 1748476818.2831926, "batch_id": "batch_4", "batch_index": 3, "processing_mode": "parallel"}, {"request_id": "req_52", "context_hash": "3039962379211266948", "sequence_length": 763, "latency": 2.0293850898742676, "success": true, "timestamp": 1748476818.2832417, "batch_id": "batch_4", "batch_index": 3, "processing_mode": "parallel"}, {"request_id": "req_59", "context_hash": "3039962379211266948", "sequence_length": 765, "latency": 2.02872633934021, "success": true, "timestamp": 1748476818.283301, "batch_id": "batch_4", "batch_index": 3, "processing_mode": "parallel"}, {"request_id": "req_49", "context_hash": "-6589667350013808819", "sequence_length": 610, "latency": 2.029165744781494, "success": true, "timestamp": 1748476818.283348, "batch_id": "batch_4", "batch_index": 3, "processing_mode": "parallel"}, {"request_id": "req_56", "context_hash": "-6589667350013808819", "sequence_length": 610, "latency": 2.0292651653289795, "success": true, "timestamp": 1748476818.283395, "batch_id": "batch_4", "batch_index": 3, "processing_mode": "parallel"}, {"request_id": "req_57", "context_hash": "-9059276493678423812", "sequence_length": 922, "latency": 2.0290346145629883, "success": true, "timestamp": 1748476818.283443, "batch_id": "batch_4", "batch_index": 3, "processing_mode": "parallel"}, {"request_id": "req_54", "context_hash": "-9059276493678423812", "sequence_length": 925, "latency": 1.800706148147583, "success": true, "timestamp": 1748476820.3121731, "batch_id": "batch_4", "batch_index": 3, "processing_mode": "parallel"}, {"request_id": "req_50", "context_hash": "-4429114490780365015", "sequence_length": 681, "latency": 1.8002705574035645, "success": true, "timestamp": 1748476820.3127434, "batch_id": "batch_4", "batch_index": 3, "processing_mode": "parallel"}, {"request_id": "req_47", "context_hash": "3591480436150793890", "sequence_length": 793, "latency": 1.8001971244812012, "success": true, "timestamp": 1748476820.3128684, "batch_id": "batch_4", "batch_index": 3, "processing_mode": "parallel"}, {"request_id": "req_48", "context_hash": "7434780898762791277", "sequence_length": 905, "latency": 1.8001363277435303, "success": true, "timestamp": 1748476820.3129704, "batch_id": "batch_4", "batch_index": 3, "processing_mode": "parallel"}, {"request_id": "req_46", "context_hash": "-8163635820091387149", "sequence_length": 893, "latency": 1.800081729888916, "success": true, "timestamp": 1748476820.3130596, "batch_id": "batch_4", "batch_index": 3, "processing_mode": "parallel"}, {"request_id": "req_62", "context_hash": "3591480436150793890", "sequence_length": 790, "latency": 3.7619237899780273, "success": true, "timestamp": 1748476822.113641, "batch_id": "batch_5", "batch_index": 4, "processing_mode": "parallel"}, {"request_id": "req_72", "context_hash": "3591480436150793890", "sequence_length": 792, "latency": 3.7616171836853027, "success": true, "timestamp": 1748476822.1138341, "batch_id": "batch_5", "batch_index": 4, "processing_mode": "parallel"}, {"request_id": "req_73", "context_hash": "3591480436150793890", "sequence_length": 792, "latency": 3.761627197265625, "success": true, "timestamp": 1748476822.113902, "batch_id": "batch_5", "batch_index": 4, "processing_mode": "parallel"}, {"request_id": "req_70", "context_hash": "-6763931301789973693", "sequence_length": 20998, "latency": 4.32377552986145, "success": true, "timestamp": 1748476822.1139581, "batch_id": "batch_5", "batch_index": 4, "processing_mode": "parallel"}, {"request_id": "req_66", "context_hash": "-6763931301789973693", "sequence_length": 21001, "latency": 4.323369979858398, "success": true, "timestamp": 1748476822.114266, "batch_id": "batch_5", "batch_index": 4, "processing_mode": "parallel"}, {"request_id": "req_75", "context_hash": "-6763931301789973693", "sequence_length": 21001, "latency": 4.323224306106567, "success": true, "timestamp": 1748476822.11455, "batch_id": "batch_5", "batch_index": 4, "processing_mode": "parallel"}, {"request_id": "req_63", "context_hash": "3341013862577558963", "sequence_length": 663, "latency": 3.7601265907287598, "success": true, "timestamp": 1748476822.114834, "batch_id": "batch_5", "batch_index": 4, "processing_mode": "parallel"}, {"request_id": "req_74", "context_hash": "3341013862577558963", "sequence_length": 665, "latency": 3.7606043815612793, "success": true, "timestamp": 1748476822.114887, "batch_id": "batch_5", "batch_index": 4, "processing_mode": "parallel"}, {"request_id": "req_71", "context_hash": "3575131124967853148", "sequence_length": 667, "latency": 3.7606618404388428, "success": true, "timestamp": 1748476822.1149366, "batch_id": "batch_5", "batch_index": 4, "processing_mode": "parallel"}, {"request_id": "req_64", "context_hash": "3575131124967853148", "sequence_length": 669, "latency": 3.7604141235351562, "success": true, "timestamp": 1748476822.1149852, "batch_id": "batch_5", "batch_index": 4, "processing_mode": "parallel"}, {"request_id": "req_69", "context_hash": "2496976890292295551", "sequence_length": 808, "latency": 2.0325522422790527, "success": true, "timestamp": 1748476825.8750858, "batch_id": "batch_5", "batch_index": 4, "processing_mode": "parallel"}, {"request_id": "req_67", "context_hash": "7330408824934193355", "sequence_length": 702, "latency": 2.0321269035339355, "success": true, "timestamp": 1748476825.8756156, "batch_id": "batch_5", "batch_index": 4, "processing_mode": "parallel"}, {"request_id": "req_68", "context_hash": "-9059276493678423812", "sequence_length": 922, "latency": 2.032033920288086, "success": true, "timestamp": 1748476825.875746, "batch_id": "batch_5", "batch_index": 4, "processing_mode": "parallel"}, {"request_id": "req_61", "context_hash": "-4429114490780365015", "sequence_length": 679, "latency": 2.031968116760254, "success": true, "timestamp": 1748476825.8758435, "batch_id": "batch_5", "batch_index": 4, "processing_mode": "parallel"}, {"request_id": "req_65", "context_hash": "3039962379211266948", "sequence_length": 766, "latency": 2.0319221019744873, "success": true, "timestamp": 1748476825.8759286, "batch_id": "batch_5", "batch_index": 4, "processing_mode": "parallel"}]}
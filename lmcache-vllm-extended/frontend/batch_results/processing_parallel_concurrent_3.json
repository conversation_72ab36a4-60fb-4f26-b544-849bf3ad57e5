{"experiment_config": {"num_batches": 5, "batch_size": 15, "repeat_ratio": 0.4, "diversity_level": 0.7, "processing_mode": "parallel", "max_concurrent": 3}, "metrics": {"total_requests": 75, "successful_requests": 75, "failed_requests": 0, "average_latency": 1.7959886582692464, "total_latency": 134.69914937019348, "throughput": 0.5567963892175563}, "detailed_results": [{"request_id": "req_6", "context_hash": "-1301696736557800362", "sequence_length": 607, "latency": 1.7602438926696777, "success": true, "timestamp": 1748476717.4153175, "batch_id": "batch_1", "batch_index": 0, "processing_mode": "parallel"}, {"request_id": "req_12", "context_hash": "-1301696736557800362", "sequence_length": 607, "latency": 1.7584469318389893, "success": true, "timestamp": 1748476717.4167306, "batch_id": "batch_1", "batch_index": 0, "processing_mode": "parallel"}, {"request_id": "req_4", "context_hash": "2771871742136583065", "sequence_length": 666, "latency": 1.7587296962738037, "success": true, "timestamp": 1748476717.4168859, "batch_id": "batch_1", "batch_index": 0, "processing_mode": "parallel"}, {"request_id": "req_9", "context_hash": "6683701638808972854", "sequence_length": 667, "latency": 1.702543020248413, "success": true, "timestamp": 1748476719.175303, "batch_id": "batch_1", "batch_index": 0, "processing_mode": "parallel"}, {"request_id": "req_11", "context_hash": "-6568629354332768756", "sequence_length": 681, "latency": 1.702488899230957, "success": true, "timestamp": 1748476719.1756353, "batch_id": "batch_1", "batch_index": 0, "processing_mode": "parallel"}, {"request_id": "req_5", "context_hash": "-6568629354332768756", "sequence_length": 682, "latency": 1.7024130821228027, "success": true, "timestamp": 1748476719.1757598, "batch_id": "batch_1", "batch_index": 0, "processing_mode": "parallel"}, {"request_id": "req_2", "context_hash": "-1100378446616262904", "sequence_length": 792, "latency": 1.7085297107696533, "success": true, "timestamp": 1748476720.8779063, "batch_id": "batch_1", "batch_index": 0, "processing_mode": "parallel"}, {"request_id": "req_8", "context_hash": "2192236488405572790", "sequence_length": 829, "latency": 1.7084903717041016, "success": true, "timestamp": 1748476720.878191, "batch_id": "batch_1", "batch_index": 0, "processing_mode": "parallel"}, {"request_id": "req_13", "context_hash": "2192236488405572790", "sequence_length": 829, "latency": 1.7084224224090576, "success": true, "timestamp": 1748476720.8783016, "batch_id": "batch_1", "batch_index": 0, "processing_mode": "parallel"}, {"request_id": "req_1", "context_hash": "5584820870497605508", "sequence_length": 883, "latency": 1.7181921005249023, "success": true, "timestamp": 1748476722.5864913, "batch_id": "batch_1", "batch_index": 0, "processing_mode": "parallel"}, {"request_id": "req_15", "context_hash": "5584820870497605508", "sequence_length": 885, "latency": 1.7181880474090576, "success": true, "timestamp": 1748476722.586741, "batch_id": "batch_1", "batch_index": 0, "processing_mode": "parallel"}, {"request_id": "req_14", "context_hash": "6523454933891273111", "sequence_length": 906, "latency": 1.718120813369751, "success": true, "timestamp": 1748476722.5868506, "batch_id": "batch_1", "batch_index": 0, "processing_mode": "parallel"}, {"request_id": "req_7", "context_hash": "6523454933891273111", "sequence_length": 907, "latency": 1.7158067226409912, "success": true, "timestamp": 1748476724.3047392, "batch_id": "batch_1", "batch_index": 0, "processing_mode": "parallel"}, {"request_id": "req_10", "context_hash": "-4917083226684776069", "sequence_length": 909, "latency": 1.715637445449829, "success": true, "timestamp": 1748476724.3049893, "batch_id": "batch_1", "batch_index": 0, "processing_mode": "parallel"}, {"request_id": "req_3", "context_hash": "-4917083226684776069", "sequence_length": 909, "latency": 1.7155678272247314, "success": true, "timestamp": 1748476724.305094, "batch_id": "batch_1", "batch_index": 0, "processing_mode": "parallel"}, {"request_id": "req_21", "context_hash": "5584820870497605508", "sequence_length": 885, "latency": 1.7104041576385498, "success": true, "timestamp": 1748476726.0213194, "batch_id": "batch_2", "batch_index": 1, "processing_mode": "parallel"}, {"request_id": "req_18", "context_hash": "-6568629354332768756", "sequence_length": 681, "latency": 1.709909439086914, "success": true, "timestamp": 1748476726.021496, "batch_id": "batch_2", "batch_index": 1, "processing_mode": "parallel"}, {"request_id": "req_23", "context_hash": "2192236488405572790", "sequence_length": 831, "latency": 1.7101187705993652, "success": true, "timestamp": 1748476726.0215626, "batch_id": "batch_2", "batch_index": 1, "processing_mode": "parallel"}, {"request_id": "req_24", "context_hash": "-1100378446616262904", "sequence_length": 791, "latency": 1.7042415142059326, "success": true, "timestamp": 1748476727.731465, "batch_id": "batch_2", "batch_index": 1, "processing_mode": "parallel"}, {"request_id": "req_16", "context_hash": "6949800670141965838", "sequence_length": 703, "latency": 1.704204797744751, "success": true, "timestamp": 1748476727.7317417, "batch_id": "batch_2", "batch_index": 1, "processing_mode": "parallel"}, {"request_id": "req_19", "context_hash": "-3347249295760635104", "sequence_length": 766, "latency": 1.7041316032409668, "success": true, "timestamp": 1748476727.731853, "batch_id": "batch_2", "batch_index": 1, "processing_mode": "parallel"}, {"request_id": "req_17", "context_hash": "8846575731091168522", "sequence_length": 807, "latency": 1.708770751953125, "success": true, "timestamp": 1748476729.4357593, "batch_id": "batch_2", "batch_index": 1, "processing_mode": "parallel"}, {"request_id": "req_30", "context_hash": "8846575731091168522", "sequence_length": 810, "latency": 1.7088394165039062, "success": true, "timestamp": 1748476729.4360008, "batch_id": "batch_2", "batch_index": 1, "processing_mode": "parallel"}, {"request_id": "req_29", "context_hash": "8846575731091168522", "sequence_length": 810, "latency": 1.7087888717651367, "success": true, "timestamp": 1748476729.436097, "batch_id": "batch_2", "batch_index": 1, "processing_mode": "parallel"}, {"request_id": "req_25", "context_hash": "8846575731091168522", "sequence_length": 810, "latency": 1.7125439643859863, "success": true, "timestamp": 1748476731.144595, "batch_id": "batch_2", "batch_index": 1, "processing_mode": "parallel"}, {"request_id": "req_26", "context_hash": "8846575731091168522", "sequence_length": 810, "latency": 1.7127835750579834, "success": true, "timestamp": 1748476731.144904, "batch_id": "batch_2", "batch_index": 1, "processing_mode": "parallel"}, {"request_id": "req_20", "context_hash": "8503816535228921940", "sequence_length": 893, "latency": 1.7127046585083008, "success": true, "timestamp": 1748476731.1450303, "batch_id": "batch_2", "batch_index": 1, "processing_mode": "parallel"}, {"request_id": "req_22", "context_hash": "-1086754319287663338", "sequence_length": 20999, "latency": 3.3192200660705566, "success": true, "timestamp": 1748476732.8571973, "batch_id": "batch_2", "batch_index": 1, "processing_mode": "parallel"}, {"request_id": "req_27", "context_hash": "-1086754319287663338", "sequence_length": 21000, "latency": 1.043677568435669, "success": true, "timestamp": 1748476732.8577535, "batch_id": "batch_2", "batch_index": 1, "processing_mode": "parallel"}, {"request_id": "req_28", "context_hash": "-1086754319287663338", "sequence_length": 21001, "latency": 3.318380117416382, "success": true, "timestamp": 1748476732.8581176, "batch_id": "batch_2", "batch_index": 1, "processing_mode": "parallel"}, {"request_id": "req_43", "context_hash": "-6568629354332768756", "sequence_length": 679, "latency": 1.6988954544067383, "success": true, "timestamp": 1748476736.1769445, "batch_id": "batch_3", "batch_index": 2, "processing_mode": "parallel"}, {"request_id": "req_44", "context_hash": "-6568629354332768756", "sequence_length": 679, "latency": 1.6990044116973877, "success": true, "timestamp": 1748476736.1771162, "batch_id": "batch_3", "batch_index": 2, "processing_mode": "parallel"}, {"request_id": "req_39", "context_hash": "-6568629354332768756", "sequence_length": 680, "latency": 1.6989798545837402, "success": true, "timestamp": 1748476736.177185, "batch_id": "batch_3", "batch_index": 2, "processing_mode": "parallel"}, {"request_id": "req_45", "context_hash": "6949800670141965838", "sequence_length": 702, "latency": 1.1872687339782715, "success": true, "timestamp": 1748476737.8758988, "batch_id": "batch_3", "batch_index": 2, "processing_mode": "parallel"}, {"request_id": "req_40", "context_hash": "6949800670141965838", "sequence_length": 702, "latency": 0.6035096645355225, "success": true, "timestamp": 1748476737.8761828, "batch_id": "batch_3", "batch_index": 2, "processing_mode": "parallel"}, {"request_id": "req_34", "context_hash": "6949800670141965838", "sequence_length": 703, "latency": 2.0607943534851074, "success": true, "timestamp": 1748476737.8762875, "batch_id": "batch_3", "batch_index": 2, "processing_mode": "parallel"}, {"request_id": "req_41", "context_hash": "2771871742136583065", "sequence_length": 664, "latency": 0.49178218841552734, "success": true, "timestamp": 1748476738.4797227, "batch_id": "batch_3", "batch_index": 2, "processing_mode": "parallel"}, {"request_id": "req_35", "context_hash": "2771871742136583065", "sequence_length": 666, "latency": 2.6766514778137207, "success": true, "timestamp": 1748476738.9715292, "batch_id": "batch_3", "batch_index": 2, "processing_mode": "parallel"}, {"request_id": "req_42", "context_hash": "-1086754319287663338", "sequence_length": 20999, "latency": 2.8640973567962646, "success": true, "timestamp": 1748476739.0631955, "batch_id": "batch_3", "batch_index": 2, "processing_mode": "parallel"}, {"request_id": "req_36", "context_hash": "-1086754319287663338", "sequence_length": 21001, "latency": 2.732912302017212, "success": true, "timestamp": 1748476739.9371095, "batch_id": "batch_3", "batch_index": 2, "processing_mode": "parallel"}, {"request_id": "req_37", "context_hash": "-4917083226684776069", "sequence_length": 911, "latency": 1.9811134338378906, "success": true, "timestamp": 1748476741.6482086, "batch_id": "batch_3", "batch_index": 2, "processing_mode": "parallel"}, {"request_id": "req_33", "context_hash": "8846575731091168522", "sequence_length": 807, "latency": 1.8963344097137451, "success": true, "timestamp": 1748476741.9273193, "batch_id": "batch_3", "batch_index": 2, "processing_mode": "parallel"}, {"request_id": "req_38", "context_hash": "2192236488405572790", "sequence_length": 829, "latency": 1.7497739791870117, "success": true, "timestamp": 1748476742.670046, "batch_id": "batch_3", "batch_index": 2, "processing_mode": "parallel"}, {"request_id": "req_32", "context_hash": "5584820870497605508", "sequence_length": 884, "latency": 1.7100715637207031, "success": true, "timestamp": 1748476743.6293442, "batch_id": "batch_3", "batch_index": 2, "processing_mode": "parallel"}, {"request_id": "req_31", "context_hash": "-2952523768322296235", "sequence_length": 922, "latency": 1.67708158493042, "success": true, "timestamp": 1748476743.823675, "batch_id": "batch_3", "batch_index": 2, "processing_mode": "parallel"}, {"request_id": "req_59", "context_hash": "-1301696736557800362", "sequence_length": 608, "latency": 0.10008001327514648, "success": true, "timestamp": 1748476745.5011063, "batch_id": "batch_4", "batch_index": 3, "processing_mode": "parallel"}, {"request_id": "req_51", "context_hash": "-1301696736557800362", "sequence_length": 609, "latency": 1.7322478294372559, "success": true, "timestamp": 1748476745.5012467, "batch_id": "batch_4", "batch_index": 3, "processing_mode": "parallel"}, {"request_id": "req_56", "context_hash": "-1301696736557800362", "sequence_length": 610, "latency": 1.7319519519805908, "success": true, "timestamp": 1748476745.5013092, "batch_id": "batch_4", "batch_index": 3, "processing_mode": "parallel"}, {"request_id": "req_60", "context_hash": "6523454933891273111", "sequence_length": 905, "latency": 1.765136480331421, "success": true, "timestamp": 1748476745.601211, "batch_id": "batch_4", "batch_index": 3, "processing_mode": "parallel"}, {"request_id": "req_50", "context_hash": "6523454933891273111", "sequence_length": 906, "latency": 2.9810030460357666, "success": true, "timestamp": 1748476747.233301, "batch_id": "batch_4", "batch_index": 3, "processing_mode": "parallel"}, {"request_id": "req_52", "context_hash": "-1086754319287663338", "sequence_length": 20999, "latency": 3.1594350337982178, "success": true, "timestamp": 1748476747.2335126, "batch_id": "batch_4", "batch_index": 3, "processing_mode": "parallel"}, {"request_id": "req_55", "context_hash": "-1086754319287663338", "sequence_length": 21001, "latency": 3.026299238204956, "success": true, "timestamp": 1748476747.3663745, "batch_id": "batch_4", "batch_index": 3, "processing_mode": "parallel"}, {"request_id": "req_53", "context_hash": "-6568629354332768756", "sequence_length": 679, "latency": 1.7777838706970215, "success": true, "timestamp": 1748476750.2143312, "batch_id": "batch_4", "batch_index": 3, "processing_mode": "parallel"}, {"request_id": "req_58", "context_hash": "-6568629354332768756", "sequence_length": 681, "latency": 1.726555585861206, "success": true, "timestamp": 1748476750.3927214, "batch_id": "batch_4", "batch_index": 3, "processing_mode": "parallel"}, {"request_id": "req_49", "context_hash": "-1100378446616262904", "sequence_length": 792, "latency": 1.726534366607666, "success": true, "timestamp": 1748476750.392968, "batch_id": "batch_4", "batch_index": 3, "processing_mode": "parallel"}, {"request_id": "req_57", "context_hash": "-1100378446616262904", "sequence_length": 793, "latency": 1.734858512878418, "success": true, "timestamp": 1748476751.99214, "batch_id": "batch_4", "batch_index": 3, "processing_mode": "parallel"}, {"request_id": "req_54", "context_hash": "2192236488405572790", "sequence_length": 832, "latency": 1.7345991134643555, "success": true, "timestamp": 1748476752.1193178, "batch_id": "batch_4", "batch_index": 3, "processing_mode": "parallel"}, {"request_id": "req_46", "context_hash": "-4917083226684776069", "sequence_length": 910, "latency": 1.7346351146697998, "success": true, "timestamp": 1748476752.1195204, "batch_id": "batch_4", "batch_index": 3, "processing_mode": "parallel"}, {"request_id": "req_47", "context_hash": "8503816535228921940", "sequence_length": 891, "latency": 1.6925718784332275, "success": true, "timestamp": 1748476753.7270257, "batch_id": "batch_4", "batch_index": 3, "processing_mode": "parallel"}, {"request_id": "req_48", "context_hash": "-3347249295760635104", "sequence_length": 763, "latency": 1.6620070934295654, "success": true, "timestamp": 1748476753.8539581, "batch_id": "batch_4", "batch_index": 3, "processing_mode": "parallel"}, {"request_id": "req_73", "context_hash": "5584820870497605508", "sequence_length": 882, "latency": 1.7148540019989014, "success": true, "timestamp": 1748476755.5163095, "batch_id": "batch_5", "batch_index": 4, "processing_mode": "parallel"}, {"request_id": "req_74", "context_hash": "5584820870497605508", "sequence_length": 884, "latency": 1.7147510051727295, "success": true, "timestamp": 1748476755.5164547, "batch_id": "batch_5", "batch_index": 4, "processing_mode": "parallel"}, {"request_id": "req_62", "context_hash": "5584820870497605508", "sequence_length": 885, "latency": 1.7143568992614746, "success": true, "timestamp": 1748476755.5165179, "batch_id": "batch_5", "batch_index": 4, "processing_mode": "parallel"}, {"request_id": "req_71", "context_hash": "5584820870497605508", "sequence_length": 885, "latency": 1.7172801494598389, "success": true, "timestamp": 1748476757.230938, "batch_id": "batch_5", "batch_index": 4, "processing_mode": "parallel"}, {"request_id": "req_69", "context_hash": "6523454933891273111", "sequence_length": 904, "latency": 1.7172482013702393, "success": true, "timestamp": 1748476757.2312226, "batch_id": "batch_5", "batch_index": 4, "processing_mode": "parallel"}, {"request_id": "req_75", "context_hash": "6523454933891273111", "sequence_length": 905, "latency": 1.7171471118927002, "success": true, "timestamp": 1748476757.231362, "batch_id": "batch_5", "batch_index": 4, "processing_mode": "parallel"}, {"request_id": "req_63", "context_hash": "6683701638808972854", "sequence_length": 666, "latency": 1.7067809104919434, "success": true, "timestamp": 1748476758.948274, "batch_id": "batch_5", "batch_index": 4, "processing_mode": "parallel"}, {"request_id": "req_70", "context_hash": "6683701638808972854", "sequence_length": 669, "latency": 1.706796407699585, "success": true, "timestamp": 1748476758.948525, "batch_id": "batch_5", "batch_index": 4, "processing_mode": "parallel"}, {"request_id": "req_72", "context_hash": "2192236488405572790", "sequence_length": 829, "latency": 1.7067344188690186, "success": true, "timestamp": 1748476758.9486277, "batch_id": "batch_5", "batch_index": 4, "processing_mode": "parallel"}, {"request_id": "req_61", "context_hash": "2192236488405572790", "sequence_length": 832, "latency": 1.7075743675231934, "success": true, "timestamp": 1748476760.6551123, "batch_id": "batch_5", "batch_index": 4, "processing_mode": "parallel"}, {"request_id": "req_68", "context_hash": "-3347249295760635104", "sequence_length": 766, "latency": 1.7075917720794678, "success": true, "timestamp": 1748476760.655378, "batch_id": "batch_5", "batch_index": 4, "processing_mode": "parallel"}, {"request_id": "req_65", "context_hash": "-6568629354332768756", "sequence_length": 682, "latency": 1.707533836364746, "success": true, "timestamp": 1748476760.6554782, "batch_id": "batch_5", "batch_index": 4, "processing_mode": "parallel"}, {"request_id": "req_67", "context_hash": "8503816535228921940", "sequence_length": 894, "latency": 1.715479850769043, "success": true, "timestamp": 1748476762.362747, "batch_id": "batch_5", "batch_index": 4, "processing_mode": "parallel"}, {"request_id": "req_66", "context_hash": "-1100378446616262904", "sequence_length": 791, "latency": 1.7152910232543945, "success": true, "timestamp": 1748476762.3630285, "batch_id": "batch_5", "batch_index": 4, "processing_mode": "parallel"}, {"request_id": "req_64", "context_hash": "-2952523768322296235", "sequence_length": 922, "latency": 1.715219259262085, "success": true, "timestamp": 1748476762.363143, "batch_id": "batch_5", "batch_index": 4, "processing_mode": "parallel"}]}
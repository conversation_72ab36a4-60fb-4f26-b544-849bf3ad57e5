{"experiment_config": {"num_batches": 5, "batch_size": 10, "repeat_ratio": 0.0, "diversity_level": 1.0, "processing_mode": "sequential", "max_concurrent": null}, "metrics": {"total_requests": 50, "successful_requests": 50, "failed_requests": 0, "average_latency": 1.7467417621612549, "total_latency": 87.33708810806274, "throughput": 0.572494470369045}, "detailed_results": [{"request_id": "req_7", "context_hash": "7632086292529355991", "sequence_length": 609, "latency": 2.7567696571350098, "success": true, "timestamp": 1748475873.5288188, "batch_id": "batch_1", "batch_index": 0, "processing_mode": "sequential"}, {"request_id": "req_10", "context_hash": "2453684239529767935", "sequence_length": 666, "latency": 1.785109519958496, "success": true, "timestamp": 1748475876.285638, "batch_id": "batch_1", "batch_index": 0, "processing_mode": "sequential"}, {"request_id": "req_8", "context_hash": "913047571477879147", "sequence_length": 669, "latency": 1.7835166454315186, "success": true, "timestamp": 1748475878.0707886, "batch_id": "batch_1", "batch_index": 0, "processing_mode": "sequential"}, {"request_id": "req_4", "context_hash": "-1591890406352665749", "sequence_length": 701, "latency": 1.7839059829711914, "success": true, "timestamp": 1748475879.8543463, "batch_id": "batch_1", "batch_index": 0, "processing_mode": "sequential"}, {"request_id": "req_6", "context_hash": "6314122438266188403", "sequence_length": 764, "latency": 1.8055107593536377, "success": true, "timestamp": 1748475881.638289, "batch_id": "batch_1", "batch_index": 0, "processing_mode": "sequential"}, {"request_id": "req_2", "context_hash": "6903392509771740762", "sequence_length": 793, "latency": 1.810495138168335, "success": true, "timestamp": 1748475883.4438324, "batch_id": "batch_1", "batch_index": 0, "processing_mode": "sequential"}, {"request_id": "req_5", "context_hash": "6234920702345427668", "sequence_length": 885, "latency": 1.8199207782745361, "success": true, "timestamp": 1748475885.254359, "batch_id": "batch_1", "batch_index": 0, "processing_mode": "sequential"}, {"request_id": "req_3", "context_hash": "5632648734791710127", "sequence_length": 892, "latency": 1.6615335941314697, "success": true, "timestamp": 1748475887.07431, "batch_id": "batch_1", "batch_index": 0, "processing_mode": "sequential"}, {"request_id": "req_1", "context_hash": "4663071350941352496", "sequence_length": 905, "latency": 1.8228652477264404, "success": true, "timestamp": 1748475888.7358723, "batch_id": "batch_1", "batch_index": 0, "processing_mode": "sequential"}, {"request_id": "req_9", "context_hash": "-2146818322392413281", "sequence_length": 21001, "latency": 8.756465673446655, "success": true, "timestamp": 1748475890.5587695, "batch_id": "batch_1", "batch_index": 0, "processing_mode": "sequential"}, {"request_id": "req_19", "context_hash": "-1591890406352665749", "sequence_length": 703, "latency": 2.1613221168518066, "success": true, "timestamp": 1748475899.3164167, "batch_id": "batch_2", "batch_index": 1, "processing_mode": "sequential"}, {"request_id": "req_20", "context_hash": "-2146818322392413281", "sequence_length": 21000, "latency": 0.4312257766723633, "success": true, "timestamp": 1748475901.477774, "batch_id": "batch_2", "batch_index": 1, "processing_mode": "sequential"}, {"request_id": "req_11", "context_hash": "7632086292529355991", "sequence_length": 608, "latency": 1.7085392475128174, "success": true, "timestamp": 1748475901.9090314, "batch_id": "batch_2", "batch_index": 1, "processing_mode": "sequential"}, {"request_id": "req_16", "context_hash": "5632648734791710127", "sequence_length": 894, "latency": 1.7303268909454346, "success": true, "timestamp": 1748475903.617598, "batch_id": "batch_2", "batch_index": 1, "processing_mode": "sequential"}, {"request_id": "req_13", "context_hash": "6314122438266188403", "sequence_length": 763, "latency": 1.7577850818634033, "success": true, "timestamp": 1748475905.347964, "batch_id": "batch_2", "batch_index": 1, "processing_mode": "sequential"}, {"request_id": "req_12", "context_hash": "4663071350941352496", "sequence_length": 907, "latency": 1.7316741943359375, "success": true, "timestamp": 1748475907.1057868, "batch_id": "batch_2", "batch_index": 1, "processing_mode": "sequential"}, {"request_id": "req_17", "context_hash": "-682391493949838419", "sequence_length": 680, "latency": 1.8000688552856445, "success": true, "timestamp": 1748475908.8374915, "batch_id": "batch_2", "batch_index": 1, "processing_mode": "sequential"}, {"request_id": "req_15", "context_hash": "-427784498560626863", "sequence_length": 831, "latency": 1.8346068859100342, "success": true, "timestamp": 1748475910.6375923, "batch_id": "batch_2", "batch_index": 1, "processing_mode": "sequential"}, {"request_id": "req_18", "context_hash": "-2317969256470104610", "sequence_length": 909, "latency": 1.8401126861572266, "success": true, "timestamp": 1748475912.4722307, "batch_id": "batch_2", "batch_index": 1, "processing_mode": "sequential"}, {"request_id": "req_14", "context_hash": "-5920792537596607517", "sequence_length": 925, "latency": 1.8419413566589355, "success": true, "timestamp": 1748475914.3123713, "batch_id": "batch_2", "batch_index": 1, "processing_mode": "sequential"}, {"request_id": "req_21", "context_hash": "5632648734791710127", "sequence_length": 894, "latency": 1.7349121570587158, "success": true, "timestamp": 1748475916.1546164, "batch_id": "batch_3", "batch_index": 2, "processing_mode": "sequential"}, {"request_id": "req_24", "context_hash": "-1591890406352665749", "sequence_length": 700, "latency": 1.7444000244140625, "success": true, "timestamp": 1748475917.8895674, "batch_id": "batch_3", "batch_index": 2, "processing_mode": "sequential"}, {"request_id": "req_26", "context_hash": "6314122438266188403", "sequence_length": 766, "latency": 1.5501885414123535, "success": true, "timestamp": 1748475919.6339982, "batch_id": "batch_3", "batch_index": 2, "processing_mode": "sequential"}, {"request_id": "req_27", "context_hash": "-5920792537596607517", "sequence_length": 922, "latency": 1.743368148803711, "success": true, "timestamp": 1748475921.1842098, "batch_id": "batch_3", "batch_index": 2, "processing_mode": "sequential"}, {"request_id": "req_23", "context_hash": "6234920702345427668", "sequence_length": 885, "latency": 0.35201168060302734, "success": true, "timestamp": 1748475922.927615, "batch_id": "batch_3", "batch_index": 2, "processing_mode": "sequential"}, {"request_id": "req_22", "context_hash": "7632086292529355991", "sequence_length": 609, "latency": 1.6488678455352783, "success": true, "timestamp": 1748475923.279653, "batch_id": "batch_3", "batch_index": 2, "processing_mode": "sequential"}, {"request_id": "req_30", "context_hash": "-2317969256470104610", "sequence_length": 912, "latency": 1.7424890995025635, "success": true, "timestamp": 1748475924.9285452, "batch_id": "batch_3", "batch_index": 2, "processing_mode": "sequential"}, {"request_id": "req_29", "context_hash": "6903392509771740762", "sequence_length": 792, "latency": 1.5629656314849854, "success": true, "timestamp": 1748475926.6710603, "batch_id": "batch_3", "batch_index": 2, "processing_mode": "sequential"}, {"request_id": "req_28", "context_hash": "4663071350941352496", "sequence_length": 904, "latency": 1.7442638874053955, "success": true, "timestamp": 1748475928.2340496, "batch_id": "batch_3", "batch_index": 2, "processing_mode": "sequential"}, {"request_id": "req_25", "context_hash": "2453684239529767935", "sequence_length": 663, "latency": 1.7442626953125, "success": true, "timestamp": 1748475929.9783387, "batch_id": "batch_3", "batch_index": 2, "processing_mode": "sequential"}, {"request_id": "req_37", "context_hash": "4663071350941352496", "sequence_length": 906, "latency": 1.7471094131469727, "success": true, "timestamp": 1748475931.722901, "batch_id": "batch_4", "batch_index": 3, "processing_mode": "sequential"}, {"request_id": "req_31", "context_hash": "-1591890406352665749", "sequence_length": 703, "latency": 1.6565358638763428, "success": true, "timestamp": 1748475933.4700365, "batch_id": "batch_4", "batch_index": 3, "processing_mode": "sequential"}, {"request_id": "req_35", "context_hash": "6314122438266188403", "sequence_length": 766, "latency": 1.7723901271820068, "success": true, "timestamp": 1748475935.126602, "batch_id": "batch_4", "batch_index": 3, "processing_mode": "sequential"}, {"request_id": "req_40", "context_hash": "-682391493949838419", "sequence_length": 679, "latency": 1.7514417171478271, "success": true, "timestamp": 1748475936.8990197, "batch_id": "batch_4", "batch_index": 3, "processing_mode": "sequential"}, {"request_id": "req_39", "context_hash": "6903392509771740762", "sequence_length": 793, "latency": 1.6585619449615479, "success": true, "timestamp": 1748475938.650489, "batch_id": "batch_4", "batch_index": 3, "processing_mode": "sequential"}, {"request_id": "req_32", "context_hash": "913047571477879147", "sequence_length": 666, "latency": 1.7460739612579346, "success": true, "timestamp": 1748475940.3090749, "batch_id": "batch_4", "batch_index": 3, "processing_mode": "sequential"}, {"request_id": "req_36", "context_hash": "-5920792537596607517", "sequence_length": 924, "latency": 1.7522644996643066, "success": true, "timestamp": 1748475942.0551744, "batch_id": "batch_4", "batch_index": 3, "processing_mode": "sequential"}, {"request_id": "req_34", "context_hash": "-2317969256470104610", "sequence_length": 912, "latency": 0.3545553684234619, "success": true, "timestamp": 1748475943.8074653, "batch_id": "batch_4", "batch_index": 3, "processing_mode": "sequential"}, {"request_id": "req_38", "context_hash": "2453684239529767935", "sequence_length": 665, "latency": 1.7514002323150635, "success": true, "timestamp": 1748475944.1620505, "batch_id": "batch_4", "batch_index": 3, "processing_mode": "sequential"}, {"request_id": "req_33", "context_hash": "1143872442876571866", "sequence_length": 809, "latency": 1.8397789001464844, "success": true, "timestamp": 1748475945.9134781, "batch_id": "batch_4", "batch_index": 3, "processing_mode": "sequential"}, {"request_id": "req_46", "context_hash": "6903392509771740762", "sequence_length": 793, "latency": 1.7360942363739014, "success": true, "timestamp": 1748475947.7535026, "batch_id": "batch_5", "batch_index": 4, "processing_mode": "sequential"}, {"request_id": "req_50", "context_hash": "4663071350941352496", "sequence_length": 906, "latency": 0.4683651924133301, "success": true, "timestamp": 1748475949.489625, "batch_id": "batch_5", "batch_index": 4, "processing_mode": "sequential"}, {"request_id": "req_43", "context_hash": "2453684239529767935", "sequence_length": 663, "latency": 1.655623435974121, "success": true, "timestamp": 1748475949.9580126, "batch_id": "batch_5", "batch_index": 4, "processing_mode": "sequential"}, {"request_id": "req_44", "context_hash": "-5920792537596607517", "sequence_length": 924, "latency": 0.2234950065612793, "success": true, "timestamp": 1748475951.6136599, "batch_id": "batch_5", "batch_index": 4, "processing_mode": "sequential"}, {"request_id": "req_42", "context_hash": "6314122438266188403", "sequence_length": 766, "latency": 1.6559576988220215, "success": true, "timestamp": 1748475951.8371768, "batch_id": "batch_5", "batch_index": 4, "processing_mode": "sequential"}, {"request_id": "req_49", "context_hash": "913047571477879147", "sequence_length": 668, "latency": 1.7488224506378174, "success": true, "timestamp": 1748475953.4931552, "batch_id": "batch_5", "batch_index": 4, "processing_mode": "sequential"}, {"request_id": "req_41", "context_hash": "1143872442876571866", "sequence_length": 809, "latency": 1.6651313304901123, "success": true, "timestamp": 1748475955.2420022, "batch_id": "batch_5", "batch_index": 4, "processing_mode": "sequential"}, {"request_id": "req_45", "context_hash": "-682391493949838419", "sequence_length": 682, "latency": 1.1498792171478271, "success": true, "timestamp": 1748475956.907156, "batch_id": "batch_5", "batch_index": 4, "processing_mode": "sequential"}, {"request_id": "req_47", "context_hash": "-427784498560626863", "sequence_length": 830, "latency": 1.348029375076294, "success": true, "timestamp": 1748475958.0570583, "batch_id": "batch_5", "batch_index": 4, "processing_mode": "sequential"}, {"request_id": "req_48", "context_hash": "-2146818322392413281", "sequence_length": 21001, "latency": 1.4641523361206055, "success": true, "timestamp": 1748475959.4051096, "batch_id": "batch_5", "batch_index": 4, "processing_mode": "sequential"}]}
{"experiment_config": {"num_batches": 5, "batch_size": 10, "repeat_ratio": 0.4, "diversity_level": 1.0, "processing_mode": "sequential", "max_concurrent": null}, "metrics": {"total_requests": 50, "successful_requests": 50, "failed_requests": 0, "average_latency": 0.39994330883026125, "total_latency": 19.99716544151306, "throughput": 2.5003543700349966}, "detailed_results": [{"request_id": "req_3", "context_hash": "7624793311737484506", "sequence_length": 668, "latency": 0.41957759857177734, "success": true, "timestamp": 1748475994.5596786, "batch_id": "batch_1", "batch_index": 0, "processing_mode": "sequential"}, {"request_id": "req_1", "context_hash": "4533474808273198831", "sequence_length": 700, "latency": 0.8210124969482422, "success": true, "timestamp": 1748475994.9792976, "batch_id": "batch_1", "batch_index": 0, "processing_mode": "sequential"}, {"request_id": "req_10", "context_hash": "4533474808273198831", "sequence_length": 702, "latency": 0.7894160747528076, "success": true, "timestamp": 1748475995.8003416, "batch_id": "batch_1", "batch_index": 0, "processing_mode": "sequential"}, {"request_id": "req_9", "context_hash": "4533474808273198831", "sequence_length": 703, "latency": 0.042380571365356445, "success": true, "timestamp": 1748475996.589785, "batch_id": "batch_1", "batch_index": 0, "processing_mode": "sequential"}, {"request_id": "req_5", "context_hash": "2228180545427402303", "sequence_length": 790, "latency": 1.441148042678833, "success": true, "timestamp": 1748475996.63219, "batch_id": "batch_1", "batch_index": 0, "processing_mode": "sequential"}, {"request_id": "req_8", "context_hash": "2228180545427402303", "sequence_length": 793, "latency": 0.2711660861968994, "success": true, "timestamp": 1748475998.0733597, "batch_id": "batch_1", "batch_index": 0, "processing_mode": "sequential"}, {"request_id": "req_6", "context_hash": "-7977984820635463737", "sequence_length": 810, "latency": 0.456179141998291, "success": true, "timestamp": 1748475998.3445463, "batch_id": "batch_1", "batch_index": 0, "processing_mode": "sequential"}, {"request_id": "req_2", "context_hash": "-6061723435844788603", "sequence_length": 894, "latency": 0.04396486282348633, "success": true, "timestamp": 1748475998.8007476, "batch_id": "batch_1", "batch_index": 0, "processing_mode": "sequential"}, {"request_id": "req_7", "context_hash": "3640678229057838622", "sequence_length": 910, "latency": 0.043051958084106445, "success": true, "timestamp": 1748475998.844732, "batch_id": "batch_1", "batch_index": 0, "processing_mode": "sequential"}, {"request_id": "req_4", "context_hash": "3640678229057838622", "sequence_length": 912, "latency": 1.0053622722625732, "success": true, "timestamp": 1748475998.8878033, "batch_id": "batch_1", "batch_index": 0, "processing_mode": "sequential"}, {"request_id": "req_18", "context_hash": "-7977984820635463737", "sequence_length": 808, "latency": 0.04274439811706543, "success": true, "timestamp": 1748475999.8935323, "batch_id": "batch_2", "batch_index": 1, "processing_mode": "sequential"}, {"request_id": "req_16", "context_hash": "-7977984820635463737", "sequence_length": 809, "latency": 0.041564226150512695, "success": true, "timestamp": 1748475999.9363077, "batch_id": "batch_2", "batch_index": 1, "processing_mode": "sequential"}, {"request_id": "req_15", "context_hash": "4533474808273198831", "sequence_length": 701, "latency": 0.0426023006439209, "success": true, "timestamp": 1748475999.9778934, "batch_id": "batch_2", "batch_index": 1, "processing_mode": "sequential"}, {"request_id": "req_20", "context_hash": "4533474808273198831", "sequence_length": 703, "latency": 0.7744512557983398, "success": true, "timestamp": 1748476000.020521, "batch_id": "batch_2", "batch_index": 1, "processing_mode": "sequential"}, {"request_id": "req_13", "context_hash": "2228180545427402303", "sequence_length": 793, "latency": 0.04300975799560547, "success": true, "timestamp": 1748476000.7949982, "batch_id": "batch_2", "batch_index": 1, "processing_mode": "sequential"}, {"request_id": "req_12", "context_hash": "6144138497754234192", "sequence_length": 679, "latency": 0.15421128273010254, "success": true, "timestamp": 1748476000.8380284, "batch_id": "batch_2", "batch_index": 1, "processing_mode": "sequential"}, {"request_id": "req_14", "context_hash": "4068107650374366425", "sequence_length": 763, "latency": 1.6544735431671143, "success": true, "timestamp": 1748476000.992269, "batch_id": "batch_2", "batch_index": 1, "processing_mode": "sequential"}, {"request_id": "req_11", "context_hash": "-656856386703265602", "sequence_length": 884, "latency": 0.1839747428894043, "success": true, "timestamp": 1748476002.6467757, "batch_id": "batch_2", "batch_index": 1, "processing_mode": "sequential"}, {"request_id": "req_19", "context_hash": "-656856386703265602", "sequence_length": 885, "latency": 0.09137701988220215, "success": true, "timestamp": 1748476002.830778, "batch_id": "batch_2", "batch_index": 1, "processing_mode": "sequential"}, {"request_id": "req_17", "context_hash": "-656856386703265602", "sequence_length": 885, "latency": 0.12908220291137695, "success": true, "timestamp": 1748476002.922174, "batch_id": "batch_2", "batch_index": 1, "processing_mode": "sequential"}, {"request_id": "req_29", "context_hash": "3640678229057838622", "sequence_length": 909, "latency": 0.043936967849731445, "success": true, "timestamp": 1748476003.0515769, "batch_id": "batch_3", "batch_index": 2, "processing_mode": "sequential"}, {"request_id": "req_24", "context_hash": "3640678229057838622", "sequence_length": 910, "latency": 0.042954444885253906, "success": true, "timestamp": 1748476003.0955458, "batch_id": "batch_3", "batch_index": 2, "processing_mode": "sequential"}, {"request_id": "req_25", "context_hash": "2228180545427402303", "sequence_length": 790, "latency": 0.3180735111236572, "success": true, "timestamp": 1748476003.138526, "batch_id": "batch_3", "batch_index": 2, "processing_mode": "sequential"}, {"request_id": "req_30", "context_hash": "2228180545427402303", "sequence_length": 793, "latency": 0.41753530502319336, "success": true, "timestamp": 1748476003.4566221, "batch_id": "batch_3", "batch_index": 2, "processing_mode": "sequential"}, {"request_id": "req_26", "context_hash": "4068107650374366425", "sequence_length": 763, "latency": 0.5634951591491699, "success": true, "timestamp": 1748476003.8741775, "batch_id": "batch_3", "batch_index": 2, "processing_mode": "sequential"}, {"request_id": "req_22", "context_hash": "7624793311737484506", "sequence_length": 668, "latency": 0.3343017101287842, "success": true, "timestamp": 1748476004.4376953, "batch_id": "batch_3", "batch_index": 2, "processing_mode": "sequential"}, {"request_id": "req_23", "context_hash": "-7000618944256088614", "sequence_length": 829, "latency": 0.0751950740814209, "success": true, "timestamp": 1748476004.7720184, "batch_id": "batch_3", "batch_index": 2, "processing_mode": "sequential"}, {"request_id": "req_28", "context_hash": "-7000618944256088614", "sequence_length": 830, "latency": 0.07603287696838379, "success": true, "timestamp": 1748476004.847234, "batch_id": "batch_3", "batch_index": 2, "processing_mode": "sequential"}, {"request_id": "req_21", "context_hash": "1072492331622349178", "sequence_length": 922, "latency": 0.8270375728607178, "success": true, "timestamp": 1748476004.9232872, "batch_id": "batch_3", "batch_index": 2, "processing_mode": "sequential"}, {"request_id": "req_27", "context_hash": "1072492331622349178", "sequence_length": 925, "latency": 0.132490873336792, "success": true, "timestamp": 1748476005.7503474, "batch_id": "batch_3", "batch_index": 2, "processing_mode": "sequential"}, {"request_id": "req_40", "context_hash": "1072492331622349178", "sequence_length": 924, "latency": 0.729515790939331, "success": true, "timestamp": 1748476005.8831332, "batch_id": "batch_4", "batch_index": 3, "processing_mode": "sequential"}, {"request_id": "req_32", "context_hash": "1072492331622349178", "sequence_length": 925, "latency": 0.043683528900146484, "success": true, "timestamp": 1748476006.6126823, "batch_id": "batch_4", "batch_index": 3, "processing_mode": "sequential"}, {"request_id": "req_38", "context_hash": "1072492331622349178", "sequence_length": 925, "latency": 0.04310965538024902, "success": true, "timestamp": 1748476006.6563878, "batch_id": "batch_4", "batch_index": 3, "processing_mode": "sequential"}, {"request_id": "req_33", "context_hash": "-6061723435844788603", "sequence_length": 891, "latency": 0.0741586685180664, "success": true, "timestamp": 1748476006.699525, "batch_id": "batch_4", "batch_index": 3, "processing_mode": "sequential"}, {"request_id": "req_37", "context_hash": "-6061723435844788603", "sequence_length": 892, "latency": 1.282702922821045, "success": true, "timestamp": 1748476006.7737055, "batch_id": "batch_4", "batch_index": 3, "processing_mode": "sequential"}, {"request_id": "req_31", "context_hash": "-656856386703265602", "sequence_length": 883, "latency": 0.1711881160736084, "success": true, "timestamp": 1748476008.0564308, "batch_id": "batch_4", "batch_index": 3, "processing_mode": "sequential"}, {"request_id": "req_34", "context_hash": "6144138497754234192", "sequence_length": 682, "latency": 0.10637807846069336, "success": true, "timestamp": 1748476008.2276416, "batch_id": "batch_4", "batch_index": 3, "processing_mode": "sequential"}, {"request_id": "req_35", "context_hash": "4068107650374366425", "sequence_length": 763, "latency": 1.1502761840820312, "success": true, "timestamp": 1748476008.33404, "batch_id": "batch_4", "batch_index": 3, "processing_mode": "sequential"}, {"request_id": "req_39", "context_hash": "-5974817990422828519", "sequence_length": 664, "latency": 0.4749305248260498, "success": true, "timestamp": 1748476009.4843361, "batch_id": "batch_4", "batch_index": 3, "processing_mode": "sequential"}, {"request_id": "req_36", "context_hash": "-5974817990422828519", "sequence_length": 665, "latency": 0.3373732566833496, "success": true, "timestamp": 1748476009.9592874, "batch_id": "batch_4", "batch_index": 3, "processing_mode": "sequential"}, {"request_id": "req_44", "context_hash": "-5974817990422828519", "sequence_length": 663, "latency": 0.17113924026489258, "success": true, "timestamp": 1748476010.2969398, "batch_id": "batch_5", "batch_index": 4, "processing_mode": "sequential"}, {"request_id": "req_48", "context_hash": "-5974817990422828519", "sequence_length": 665, "latency": 0.041944265365600586, "success": true, "timestamp": 1748476010.468109, "batch_id": "batch_5", "batch_index": 4, "processing_mode": "sequential"}, {"request_id": "req_43", "context_hash": "-656856386703265602", "sequence_length": 885, "latency": 0.043489694595336914, "success": true, "timestamp": 1748476010.5100746, "batch_id": "batch_5", "batch_index": 4, "processing_mode": "sequential"}, {"request_id": "req_41", "context_hash": "4068107650374366425", "sequence_length": 765, "latency": 0.1536719799041748, "success": true, "timestamp": 1748476010.553588, "batch_id": "batch_5", "batch_index": 4, "processing_mode": "sequential"}, {"request_id": "req_45", "context_hash": "2228180545427402303", "sequence_length": 791, "latency": 0.11195755004882812, "success": true, "timestamp": 1748476010.7072828, "batch_id": "batch_5", "batch_index": 4, "processing_mode": "sequential"}, {"request_id": "req_46", "context_hash": "-7000618944256088614", "sequence_length": 829, "latency": 0.07471656799316406, "success": true, "timestamp": 1748476010.8192666, "batch_id": "batch_5", "batch_index": 4, "processing_mode": "sequential"}, {"request_id": "req_50", "context_hash": "3297958256748599958", "sequence_length": 905, "latency": 0.12446451187133789, "success": true, "timestamp": 1748476010.894003, "batch_id": "batch_5", "batch_index": 4, "processing_mode": "sequential"}, {"request_id": "req_49", "context_hash": "3297958256748599958", "sequence_length": 905, "latency": 1.6627769470214844, "success": true, "timestamp": 1748476011.0184882, "batch_id": "batch_5", "batch_index": 4, "processing_mode": "sequential"}, {"request_id": "req_42", "context_hash": "3297958256748599958", "sequence_length": 906, "latency": 1.664020299911499, "success": true, "timestamp": 1748476012.6812909, "batch_id": "batch_5", "batch_index": 4, "processing_mode": "sequential"}, {"request_id": "req_47", "context_hash": "3297958256748599958", "sequence_length": 907, "latency": 0.21386432647705078, "success": true, "timestamp": 1748476014.3453364, "batch_id": "batch_5", "batch_index": 4, "processing_mode": "sequential"}]}
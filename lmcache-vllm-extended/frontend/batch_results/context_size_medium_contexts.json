{"experiment_config": {"num_batches": 5, "batch_size": 10, "repeat_ratio": 0.2, "diversity_level": 0.6, "processing_mode": "sequential", "max_concurrent": null}, "metrics": {"total_requests": 50, "successful_requests": 50, "failed_requests": 0, "average_latency": 1.6533129405975342, "total_latency": 82.66564702987671, "throughput": 0.6048461700412165}, "detailed_results": [{"request_id": "req_9", "context_hash": "-2416730631381398368", "sequence_length": 607, "latency": 1.6887094974517822, "success": true, "timestamp": 1748476398.649567, "batch_id": "batch_1", "batch_index": 0, "processing_mode": "sequential"}, {"request_id": "req_4", "context_hash": "-2416730631381398368", "sequence_length": 609, "latency": 1.6327314376831055, "success": true, "timestamp": 1748476400.3383303, "batch_id": "batch_1", "batch_index": 0, "processing_mode": "sequential"}, {"request_id": "req_5", "context_hash": "-1377022548808141938", "sequence_length": 666, "latency": 1.6360678672790527, "success": true, "timestamp": 1748476401.9710915, "batch_id": "batch_1", "batch_index": 0, "processing_mode": "sequential"}, {"request_id": "req_8", "context_hash": "5810705039255670543", "sequence_length": 679, "latency": 1.6356995105743408, "success": true, "timestamp": 1748476403.607184, "batch_id": "batch_1", "batch_index": 0, "processing_mode": "sequential"}, {"request_id": "req_3", "context_hash": "-2949304336987485148", "sequence_length": 763, "latency": 1.6381134986877441, "success": true, "timestamp": 1748476405.2429078, "batch_id": "batch_1", "batch_index": 0, "processing_mode": "sequential"}, {"request_id": "req_6", "context_hash": "-4249825753524886761", "sequence_length": 792, "latency": 1.6412889957427979, "success": true, "timestamp": 1748476406.881045, "batch_id": "batch_1", "batch_index": 0, "processing_mode": "sequential"}, {"request_id": "req_7", "context_hash": "5114057725758054139", "sequence_length": 830, "latency": 1.641031265258789, "success": true, "timestamp": 1748476408.522357, "batch_id": "batch_1", "batch_index": 0, "processing_mode": "sequential"}, {"request_id": "req_10", "context_hash": "-1195570907802172633", "sequence_length": 904, "latency": 1.6437056064605713, "success": true, "timestamp": 1748476410.16341, "batch_id": "batch_1", "batch_index": 0, "processing_mode": "sequential"}, {"request_id": "req_1", "context_hash": "-1195570907802172633", "sequence_length": 907, "latency": 1.6405041217803955, "success": true, "timestamp": 1748476411.8071373, "batch_id": "batch_1", "batch_index": 0, "processing_mode": "sequential"}, {"request_id": "req_2", "context_hash": "-2981172086661262081", "sequence_length": 925, "latency": 1.6461498737335205, "success": true, "timestamp": 1748476413.4476633, "batch_id": "batch_1", "batch_index": 0, "processing_mode": "sequential"}, {"request_id": "req_19", "context_hash": "-2416730631381398368", "sequence_length": 607, "latency": 1.6373140811920166, "success": true, "timestamp": 1748476415.0943167, "batch_id": "batch_2", "batch_index": 1, "processing_mode": "sequential"}, {"request_id": "req_17", "context_hash": "-2416730631381398368", "sequence_length": 608, "latency": 1.6367533206939697, "success": true, "timestamp": 1748476416.7316613, "batch_id": "batch_2", "batch_index": 1, "processing_mode": "sequential"}, {"request_id": "req_11", "context_hash": "5114057725758054139", "sequence_length": 832, "latency": 1.646407127380371, "success": true, "timestamp": 1748476418.3684428, "batch_id": "batch_2", "batch_index": 1, "processing_mode": "sequential"}, {"request_id": "req_15", "context_hash": "-4249825753524886761", "sequence_length": 790, "latency": 1.641387701034546, "success": true, "timestamp": 1748476420.0148783, "batch_id": "batch_2", "batch_index": 1, "processing_mode": "sequential"}, {"request_id": "req_12", "context_hash": "-1377022548808141938", "sequence_length": 664, "latency": 1.6410629749298096, "success": true, "timestamp": 1748476421.656293, "batch_id": "batch_2", "batch_index": 1, "processing_mode": "sequential"}, {"request_id": "req_14", "context_hash": "-1195570907802172633", "sequence_length": 907, "latency": 1.6476001739501953, "success": true, "timestamp": 1748476423.2973828, "batch_id": "batch_2", "batch_index": 1, "processing_mode": "sequential"}, {"request_id": "req_13", "context_hash": "-4848178205586265639", "sequence_length": 668, "latency": 1.6381535530090332, "success": true, "timestamp": 1748476424.9450095, "batch_id": "batch_2", "batch_index": 1, "processing_mode": "sequential"}, {"request_id": "req_16", "context_hash": "-3095412989379987056", "sequence_length": 701, "latency": 1.6381096839904785, "success": true, "timestamp": 1748476426.583193, "batch_id": "batch_2", "batch_index": 1, "processing_mode": "sequential"}, {"request_id": "req_20", "context_hash": "-2201819261980756628", "sequence_length": 912, "latency": 1.6438679695129395, "success": true, "timestamp": 1748476428.2213302, "batch_id": "batch_2", "batch_index": 1, "processing_mode": "sequential"}, {"request_id": "req_18", "context_hash": "-2201819261980756628", "sequence_length": 912, "latency": 1.6425766944885254, "success": true, "timestamp": 1748476429.8652277, "batch_id": "batch_2", "batch_index": 1, "processing_mode": "sequential"}, {"request_id": "req_29", "context_hash": "-4848178205586265639", "sequence_length": 666, "latency": 1.6393704414367676, "success": true, "timestamp": 1748476431.5081544, "batch_id": "batch_3", "batch_index": 2, "processing_mode": "sequential"}, {"request_id": "req_27", "context_hash": "-4848178205586265639", "sequence_length": 669, "latency": 1.6381933689117432, "success": true, "timestamp": 1748476433.1475515, "batch_id": "batch_3", "batch_index": 2, "processing_mode": "sequential"}, {"request_id": "req_26", "context_hash": "5810705039255670543", "sequence_length": 680, "latency": 1.6409151554107666, "success": true, "timestamp": 1748476434.7857726, "batch_id": "batch_3", "batch_index": 2, "processing_mode": "sequential"}, {"request_id": "req_30", "context_hash": "5810705039255670543", "sequence_length": 680, "latency": 1.6400654315948486, "success": true, "timestamp": 1748476436.4267144, "batch_id": "batch_3", "batch_index": 2, "processing_mode": "sequential"}, {"request_id": "req_28", "context_hash": "-2981172086661262081", "sequence_length": 925, "latency": 1.642153263092041, "success": true, "timestamp": 1748476438.0668066, "batch_id": "batch_3", "batch_index": 2, "processing_mode": "sequential"}, {"request_id": "req_25", "context_hash": "-2201819261980756628", "sequence_length": 912, "latency": 1.6440770626068115, "success": true, "timestamp": 1748476439.708995, "batch_id": "batch_3", "batch_index": 2, "processing_mode": "sequential"}, {"request_id": "req_21", "context_hash": "-2416730631381398368", "sequence_length": 610, "latency": 1.638235092163086, "success": true, "timestamp": 1748476441.3531044, "batch_id": "batch_3", "batch_index": 2, "processing_mode": "sequential"}, {"request_id": "req_23", "context_hash": "-7074139870164444074", "sequence_length": 882, "latency": 1.6440792083740234, "success": true, "timestamp": 1748476442.9913712, "batch_id": "batch_3", "batch_index": 2, "processing_mode": "sequential"}, {"request_id": "req_22", "context_hash": "-687963835613136254", "sequence_length": 892, "latency": 1.645301103591919, "success": true, "timestamp": 1748476444.6354802, "batch_id": "batch_3", "batch_index": 2, "processing_mode": "sequential"}, {"request_id": "req_24", "context_hash": "-3532647355796432810", "sequence_length": 21000, "latency": 2.333134174346924, "success": true, "timestamp": 1748476446.2808092, "batch_id": "batch_3", "batch_index": 2, "processing_mode": "sequential"}, {"request_id": "req_39", "context_hash": "5810705039255670543", "sequence_length": 679, "latency": 1.6349174976348877, "success": true, "timestamp": 1748476448.614177, "batch_id": "batch_4", "batch_index": 3, "processing_mode": "sequential"}, {"request_id": "req_32", "context_hash": "5810705039255670543", "sequence_length": 682, "latency": 1.6406373977661133, "success": true, "timestamp": 1748476450.2491255, "batch_id": "batch_4", "batch_index": 3, "processing_mode": "sequential"}, {"request_id": "req_40", "context_hash": "-3532647355796432810", "sequence_length": 21000, "latency": 1.3856499195098877, "success": true, "timestamp": 1748476451.8897943, "batch_id": "batch_4", "batch_index": 3, "processing_mode": "sequential"}, {"request_id": "req_34", "context_hash": "-3532647355796432810", "sequence_length": 21001, "latency": 1.0716392993927002, "success": true, "timestamp": 1748476453.2754767, "batch_id": "batch_4", "batch_index": 3, "processing_mode": "sequential"}, {"request_id": "req_35", "context_hash": "-687963835613136254", "sequence_length": 891, "latency": 1.63869309425354, "success": true, "timestamp": 1748476454.3471475, "batch_id": "batch_4", "batch_index": 3, "processing_mode": "sequential"}, {"request_id": "req_37", "context_hash": "-2949304336987485148", "sequence_length": 766, "latency": 1.6437125205993652, "success": true, "timestamp": 1748476455.985872, "batch_id": "batch_4", "batch_index": 3, "processing_mode": "sequential"}, {"request_id": "req_33", "context_hash": "-4249825753524886761", "sequence_length": 793, "latency": 1.6414058208465576, "success": true, "timestamp": 1748476457.6296127, "batch_id": "batch_4", "batch_index": 3, "processing_mode": "sequential"}, {"request_id": "req_38", "context_hash": "-4848178205586265639", "sequence_length": 667, "latency": 1.6393303871154785, "success": true, "timestamp": 1748476459.2710433, "batch_id": "batch_4", "batch_index": 3, "processing_mode": "sequential"}, {"request_id": "req_31", "context_hash": "5114057725758054139", "sequence_length": 831, "latency": 1.6456120014190674, "success": true, "timestamp": 1748476460.9104, "batch_id": "batch_4", "batch_index": 3, "processing_mode": "sequential"}, {"request_id": "req_36", "context_hash": "-1195570907802172633", "sequence_length": 907, "latency": 1.6463992595672607, "success": true, "timestamp": 1748476462.5560393, "batch_id": "batch_4", "batch_index": 3, "processing_mode": "sequential"}, {"request_id": "req_47", "context_hash": "-4848178205586265639", "sequence_length": 668, "latency": 1.6395719051361084, "success": true, "timestamp": 1748476464.202667, "batch_id": "batch_5", "batch_index": 4, "processing_mode": "sequential"}, {"request_id": "req_50", "context_hash": "-4848178205586265639", "sequence_length": 669, "latency": 1.641003131866455, "success": true, "timestamp": 1748476465.8422694, "batch_id": "batch_5", "batch_index": 4, "processing_mode": "sequential"}, {"request_id": "req_43", "context_hash": "-7074139870164444074", "sequence_length": 882, "latency": 1.6462171077728271, "success": true, "timestamp": 1748476467.4832995, "batch_id": "batch_5", "batch_index": 4, "processing_mode": "sequential"}, {"request_id": "req_49", "context_hash": "-7074139870164444074", "sequence_length": 885, "latency": 1.6460456848144531, "success": true, "timestamp": 1748476469.129542, "batch_id": "batch_5", "batch_index": 4, "processing_mode": "sequential"}, {"request_id": "req_45", "context_hash": "-4249825753524886761", "sequence_length": 792, "latency": 1.6446046829223633, "success": true, "timestamp": 1748476470.7756126, "batch_id": "batch_5", "batch_index": 4, "processing_mode": "sequential"}, {"request_id": "req_41", "context_hash": "-1195570907802172633", "sequence_length": 905, "latency": 1.6458196640014648, "success": true, "timestamp": 1748476472.42024, "batch_id": "batch_5", "batch_index": 4, "processing_mode": "sequential"}, {"request_id": "req_48", "context_hash": "-2201819261980756628", "sequence_length": 912, "latency": 1.6463217735290527, "success": true, "timestamp": 1748476474.0660849, "batch_id": "batch_5", "batch_index": 4, "processing_mode": "sequential"}, {"request_id": "req_42", "context_hash": "5114057725758054139", "sequence_length": 832, "latency": 1.6425740718841553, "success": true, "timestamp": 1748476475.7124312, "batch_id": "batch_5", "batch_index": 4, "processing_mode": "sequential"}, {"request_id": "req_44", "context_hash": "-3532647355796432810", "sequence_length": 21001, "latency": 2.322036027908325, "success": true, "timestamp": 1748476477.3550296, "batch_id": "batch_5", "batch_index": 4, "processing_mode": "sequential"}, {"request_id": "req_46", "context_hash": "-2981172086661262081", "sequence_length": 925, "latency": 1.6406965255737305, "success": true, "timestamp": 1748476479.6770968, "batch_id": "batch_5", "batch_index": 4, "processing_mode": "sequential"}]}
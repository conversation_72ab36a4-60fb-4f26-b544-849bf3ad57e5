{"experiment_config": {"num_batches": 5, "batch_size": 15, "repeat_ratio": 0.3, "diversity_level": 0.8, "processing_mode": "sequential", "max_concurrent": null}, "metrics": {"total_requests": 75, "successful_requests": 75, "failed_requests": 0, "average_latency": 1.6641995747884115, "total_latency": 124.81496810913086, "throughput": 0.6008894697182826}, "detailed_results": [{"request_id": "req_5", "context_hash": "-7390988619282666577", "sequence_length": 610, "latency": 1.675492525100708, "success": true, "timestamp": 1748476135.7702198, "batch_id": "batch_1", "batch_index": 0, "processing_mode": "sequential"}, {"request_id": "req_10", "context_hash": "-8578968415085925033", "sequence_length": 665, "latency": 1.6312341690063477, "success": true, "timestamp": 1748476137.44575, "batch_id": "batch_1", "batch_index": 0, "processing_mode": "sequential"}, {"request_id": "req_15", "context_hash": "2773063400750395084", "sequence_length": 679, "latency": 1.6357388496398926, "success": true, "timestamp": 1748476139.0770135, "batch_id": "batch_1", "batch_index": 0, "processing_mode": "sequential"}, {"request_id": "req_11", "context_hash": "2773063400750395084", "sequence_length": 681, "latency": 1.634718656539917, "success": true, "timestamp": 1748476140.7127762, "batch_id": "batch_1", "batch_index": 0, "processing_mode": "sequential"}, {"request_id": "req_14", "context_hash": "2773063400750395084", "sequence_length": 682, "latency": 1.6354632377624512, "success": true, "timestamp": 1748476142.3475184, "batch_id": "batch_1", "batch_index": 0, "processing_mode": "sequential"}, {"request_id": "req_9", "context_hash": "9104159111137505293", "sequence_length": 765, "latency": 1.6364898681640625, "success": true, "timestamp": 1748476143.983003, "batch_id": "batch_1", "batch_index": 0, "processing_mode": "sequential"}, {"request_id": "req_6", "context_hash": "-4933244834061904653", "sequence_length": 790, "latency": 1.6376886367797852, "success": true, "timestamp": 1748476145.6195138, "batch_id": "batch_1", "batch_index": 0, "processing_mode": "sequential"}, {"request_id": "req_3", "context_hash": "-7766395146614640456", "sequence_length": 808, "latency": 1.638258934020996, "success": true, "timestamp": 1748476147.2572215, "batch_id": "batch_1", "batch_index": 0, "processing_mode": "sequential"}, {"request_id": "req_13", "context_hash": "-9001221759740086003", "sequence_length": 830, "latency": 1.640336275100708, "success": true, "timestamp": 1748476148.8954997, "batch_id": "batch_1", "batch_index": 0, "processing_mode": "sequential"}, {"request_id": "req_12", "context_hash": "-9001221759740086003", "sequence_length": 832, "latency": 1.6382842063903809, "success": true, "timestamp": 1748476150.5358553, "batch_id": "batch_1", "batch_index": 0, "processing_mode": "sequential"}, {"request_id": "req_4", "context_hash": "-9001221759740086003", "sequence_length": 832, "latency": 1.6388003826141357, "success": true, "timestamp": 1748476152.1741598, "batch_id": "batch_1", "batch_index": 0, "processing_mode": "sequential"}, {"request_id": "req_8", "context_hash": "-7323124880561240370", "sequence_length": 906, "latency": 1.642425298690796, "success": true, "timestamp": 1748476153.8129787, "batch_id": "batch_1", "batch_index": 0, "processing_mode": "sequential"}, {"request_id": "req_2", "context_hash": "-7809262960343837230", "sequence_length": 912, "latency": 1.6430425643920898, "success": true, "timestamp": 1748476155.455425, "batch_id": "batch_1", "batch_index": 0, "processing_mode": "sequential"}, {"request_id": "req_7", "context_hash": "4564363324709753927", "sequence_length": 924, "latency": 1.6450059413909912, "success": true, "timestamp": 1748476157.098491, "batch_id": "batch_1", "batch_index": 0, "processing_mode": "sequential"}, {"request_id": "req_1", "context_hash": "-5893263423773287004", "sequence_length": 21001, "latency": 2.328965902328491, "success": true, "timestamp": 1748476158.7435205, "batch_id": "batch_1", "batch_index": 0, "processing_mode": "sequential"}, {"request_id": "req_22", "context_hash": "-7766395146614640456", "sequence_length": 807, "latency": 1.6410329341888428, "success": true, "timestamp": 1748476161.0729153, "batch_id": "batch_2", "batch_index": 1, "processing_mode": "sequential"}, {"request_id": "req_28", "context_hash": "-7766395146614640456", "sequence_length": 809, "latency": 1.646744966506958, "success": true, "timestamp": 1748476162.7139785, "batch_id": "batch_2", "batch_index": 1, "processing_mode": "sequential"}, {"request_id": "req_27", "context_hash": "-7323124880561240370", "sequence_length": 905, "latency": 1.6434111595153809, "success": true, "timestamp": 1748476164.3607497, "batch_id": "batch_2", "batch_index": 1, "processing_mode": "sequential"}, {"request_id": "req_21", "context_hash": "-7323124880561240370", "sequence_length": 906, "latency": 1.6422357559204102, "success": true, "timestamp": 1748476166.0041873, "batch_id": "batch_2", "batch_index": 1, "processing_mode": "sequential"}, {"request_id": "req_29", "context_hash": "-7390988619282666577", "sequence_length": 610, "latency": 1.6386873722076416, "success": true, "timestamp": 1748476167.6464496, "batch_id": "batch_2", "batch_index": 1, "processing_mode": "sequential"}, {"request_id": "req_18", "context_hash": "-7390988619282666577", "sequence_length": 610, "latency": 1.6384000778198242, "success": true, "timestamp": 1748476169.285162, "batch_id": "batch_2", "batch_index": 1, "processing_mode": "sequential"}, {"request_id": "req_17", "context_hash": "-7809262960343837230", "sequence_length": 910, "latency": 1.6426877975463867, "success": true, "timestamp": 1748476170.9235864, "batch_id": "batch_2", "batch_index": 1, "processing_mode": "sequential"}, {"request_id": "req_25", "context_hash": "-4933244834061904653", "sequence_length": 790, "latency": 1.6451964378356934, "success": true, "timestamp": 1748476172.566297, "batch_id": "batch_2", "batch_index": 1, "processing_mode": "sequential"}, {"request_id": "req_24", "context_hash": "2773063400750395084", "sequence_length": 682, "latency": 1.640409231185913, "success": true, "timestamp": 1748476174.2115154, "batch_id": "batch_2", "batch_index": 1, "processing_mode": "sequential"}, {"request_id": "req_19", "context_hash": "9104159111137505293", "sequence_length": 764, "latency": 1.6442861557006836, "success": true, "timestamp": 1748476175.8519545, "batch_id": "batch_2", "batch_index": 1, "processing_mode": "sequential"}, {"request_id": "req_16", "context_hash": "2807764063746446535", "sequence_length": 667, "latency": 1.641209363937378, "success": true, "timestamp": 1748476177.4962656, "batch_id": "batch_2", "batch_index": 1, "processing_mode": "sequential"}, {"request_id": "req_30", "context_hash": "2807764063746446535", "sequence_length": 669, "latency": 1.6395153999328613, "success": true, "timestamp": 1748476179.137501, "batch_id": "batch_2", "batch_index": 1, "processing_mode": "sequential"}, {"request_id": "req_20", "context_hash": "-8570604672373697648", "sequence_length": 703, "latency": 1.6393406391143799, "success": true, "timestamp": 1748476180.7770426, "batch_id": "batch_2", "batch_index": 1, "processing_mode": "sequential"}, {"request_id": "req_26", "context_hash": "757700573637916757", "sequence_length": 884, "latency": 1.6455512046813965, "success": true, "timestamp": 1748476182.416408, "batch_id": "batch_2", "batch_index": 1, "processing_mode": "sequential"}, {"request_id": "req_23", "context_hash": "6825330583792476391", "sequence_length": 892, "latency": 1.645031452178955, "success": true, "timestamp": 1748476184.0619888, "batch_id": "batch_2", "batch_index": 1, "processing_mode": "sequential"}, {"request_id": "req_44", "context_hash": "757700573637916757", "sequence_length": 882, "latency": 1.6449894905090332, "success": true, "timestamp": 1748476185.7083676, "batch_id": "batch_3", "batch_index": 2, "processing_mode": "sequential"}, {"request_id": "req_38", "context_hash": "757700573637916757", "sequence_length": 884, "latency": 1.6482274532318115, "success": true, "timestamp": 1748476187.3533988, "batch_id": "batch_3", "batch_index": 2, "processing_mode": "sequential"}, {"request_id": "req_42", "context_hash": "757700573637916757", "sequence_length": 885, "latency": 1.6490075588226318, "success": true, "timestamp": 1748476189.001653, "batch_id": "batch_3", "batch_index": 2, "processing_mode": "sequential"}, {"request_id": "req_39", "context_hash": "-4933244834061904653", "sequence_length": 790, "latency": 1.6442561149597168, "success": true, "timestamp": 1748476190.6506858, "batch_id": "batch_3", "batch_index": 2, "processing_mode": "sequential"}, {"request_id": "req_45", "context_hash": "-4933244834061904653", "sequence_length": 793, "latency": 1.6465568542480469, "success": true, "timestamp": 1748476192.2949646, "batch_id": "batch_3", "batch_index": 2, "processing_mode": "sequential"}, {"request_id": "req_43", "context_hash": "-8570604672373697648", "sequence_length": 703, "latency": 1.6451833248138428, "success": true, "timestamp": 1748476193.9415429, "batch_id": "batch_3", "batch_index": 2, "processing_mode": "sequential"}, {"request_id": "req_31", "context_hash": "-8570604672373697648", "sequence_length": 703, "latency": 1.643845796585083, "success": true, "timestamp": 1748476195.5867484, "batch_id": "batch_3", "batch_index": 2, "processing_mode": "sequential"}, {"request_id": "req_34", "context_hash": "-7390988619282666577", "sequence_length": 609, "latency": 1.6425769329071045, "success": true, "timestamp": 1748476197.2306166, "batch_id": "batch_3", "batch_index": 2, "processing_mode": "sequential"}, {"request_id": "req_37", "context_hash": "4564363324709753927", "sequence_length": 922, "latency": 1.6510887145996094, "success": true, "timestamp": 1748476198.8732145, "batch_id": "batch_3", "batch_index": 2, "processing_mode": "sequential"}, {"request_id": "req_40", "context_hash": "2807764063746446535", "sequence_length": 668, "latency": 1.6444690227508545, "success": true, "timestamp": 1748476200.5243251, "batch_id": "batch_3", "batch_index": 2, "processing_mode": "sequential"}, {"request_id": "req_35", "context_hash": "9104159111137505293", "sequence_length": 766, "latency": 1.647902011871338, "success": true, "timestamp": 1748476202.1688166, "batch_id": "batch_3", "batch_index": 2, "processing_mode": "sequential"}, {"request_id": "req_32", "context_hash": "-9001221759740086003", "sequence_length": 832, "latency": 1.6453649997711182, "success": true, "timestamp": 1748476203.816739, "batch_id": "batch_3", "batch_index": 2, "processing_mode": "sequential"}, {"request_id": "req_36", "context_hash": "6825330583792476391", "sequence_length": 893, "latency": 1.6487133502960205, "success": true, "timestamp": 1748476205.462126, "batch_id": "batch_3", "batch_index": 2, "processing_mode": "sequential"}, {"request_id": "req_41", "context_hash": "-7809262960343837230", "sequence_length": 911, "latency": 1.6480505466461182, "success": true, "timestamp": 1748476207.1108742, "batch_id": "batch_3", "batch_index": 2, "processing_mode": "sequential"}, {"request_id": "req_33", "context_hash": "-7323124880561240370", "sequence_length": 904, "latency": 1.6489577293395996, "success": true, "timestamp": 1748476208.758957, "batch_id": "batch_3", "batch_index": 2, "processing_mode": "sequential"}, {"request_id": "req_55", "context_hash": "-7766395146614640456", "sequence_length": 808, "latency": 1.648134708404541, "success": true, "timestamp": 1748476210.4082947, "batch_id": "batch_4", "batch_index": 3, "processing_mode": "sequential"}, {"request_id": "req_58", "context_hash": "-7766395146614640456", "sequence_length": 810, "latency": 1.6456937789916992, "success": true, "timestamp": 1748476212.0564585, "batch_id": "batch_4", "batch_index": 3, "processing_mode": "sequential"}, {"request_id": "req_59", "context_hash": "-7809262960343837230", "sequence_length": 910, "latency": 1.6514182090759277, "success": true, "timestamp": 1748476213.702176, "batch_id": "batch_4", "batch_index": 3, "processing_mode": "sequential"}, {"request_id": "req_53", "context_hash": "-7809262960343837230", "sequence_length": 911, "latency": 1.6528449058532715, "success": true, "timestamp": 1748476215.3536215, "batch_id": "batch_4", "batch_index": 3, "processing_mode": "sequential"}, {"request_id": "req_57", "context_hash": "2773063400750395084", "sequence_length": 681, "latency": 1.6454017162322998, "success": true, "timestamp": 1748476217.006491, "batch_id": "batch_4", "batch_index": 3, "processing_mode": "sequential"}, {"request_id": "req_51", "context_hash": "2773063400750395084", "sequence_length": 682, "latency": 1.6475763320922852, "success": true, "timestamp": 1748476218.6519167, "batch_id": "batch_4", "batch_index": 3, "processing_mode": "sequential"}, {"request_id": "req_60", "context_hash": "-4933244834061904653", "sequence_length": 793, "latency": 1.648749828338623, "success": true, "timestamp": 1748476220.2995179, "batch_id": "batch_4", "batch_index": 3, "processing_mode": "sequential"}, {"request_id": "req_46", "context_hash": "-4933244834061904653", "sequence_length": 793, "latency": 1.6482713222503662, "success": true, "timestamp": 1748476221.948289, "batch_id": "batch_4", "batch_index": 3, "processing_mode": "sequential"}, {"request_id": "req_52", "context_hash": "-8570604672373697648", "sequence_length": 700, "latency": 1.6469204425811768, "success": true, "timestamp": 1748476223.5965798, "batch_id": "batch_4", "batch_index": 3, "processing_mode": "sequential"}, {"request_id": "req_54", "context_hash": "4564363324709753927", "sequence_length": 925, "latency": 1.6496336460113525, "success": true, "timestamp": 1748476225.243523, "batch_id": "batch_4", "batch_index": 3, "processing_mode": "sequential"}, {"request_id": "req_48", "context_hash": "757700573637916757", "sequence_length": 885, "latency": 1.6496288776397705, "success": true, "timestamp": 1748476226.893179, "batch_id": "batch_4", "batch_index": 3, "processing_mode": "sequential"}, {"request_id": "req_50", "context_hash": "-8578968415085925033", "sequence_length": 664, "latency": 1.6441118717193604, "success": true, "timestamp": 1748476228.5428298, "batch_id": "batch_4", "batch_index": 3, "processing_mode": "sequential"}, {"request_id": "req_56", "context_hash": "-9001221759740086003", "sequence_length": 830, "latency": 1.6471941471099854, "success": true, "timestamp": 1748476230.186969, "batch_id": "batch_4", "batch_index": 3, "processing_mode": "sequential"}, {"request_id": "req_49", "context_hash": "-7390988619282666577", "sequence_length": 610, "latency": 1.6438193321228027, "success": true, "timestamp": 1748476231.8341868, "batch_id": "batch_4", "batch_index": 3, "processing_mode": "sequential"}, {"request_id": "req_47", "context_hash": "9104159111137505293", "sequence_length": 763, "latency": 1.6446163654327393, "success": true, "timestamp": 1748476233.4780293, "batch_id": "batch_4", "batch_index": 3, "processing_mode": "sequential"}, {"request_id": "req_75", "context_hash": "4564363324709753927", "sequence_length": 923, "latency": 1.6493308544158936, "success": true, "timestamp": 1748476235.1228912, "batch_id": "batch_5", "batch_index": 4, "processing_mode": "sequential"}, {"request_id": "req_65", "context_hash": "4564363324709753927", "sequence_length": 925, "latency": 1.648937463760376, "success": true, "timestamp": 1748476236.7722461, "batch_id": "batch_5", "batch_index": 4, "processing_mode": "sequential"}, {"request_id": "req_66", "context_hash": "-9001221759740086003", "sequence_length": 829, "latency": 1.6502103805541992, "success": true, "timestamp": 1748476238.421207, "batch_id": "batch_5", "batch_index": 4, "processing_mode": "sequential"}, {"request_id": "req_73", "context_hash": "-9001221759740086003", "sequence_length": 832, "latency": 1.6494934558868408, "success": true, "timestamp": 1748476240.0714407, "batch_id": "batch_5", "batch_index": 4, "processing_mode": "sequential"}, {"request_id": "req_62", "context_hash": "757700573637916757", "sequence_length": 884, "latency": 1.6483736038208008, "success": true, "timestamp": 1748476241.720955, "batch_id": "batch_5", "batch_index": 4, "processing_mode": "sequential"}, {"request_id": "req_74", "context_hash": "757700573637916757", "sequence_length": 885, "latency": 1.6481220722198486, "success": true, "timestamp": 1748476243.3693516, "batch_id": "batch_5", "batch_index": 4, "processing_mode": "sequential"}, {"request_id": "req_72", "context_hash": "2773063400750395084", "sequence_length": 681, "latency": 1.6446402072906494, "success": true, "timestamp": 1748476245.0174963, "batch_id": "batch_5", "batch_index": 4, "processing_mode": "sequential"}, {"request_id": "req_61", "context_hash": "2773063400750395084", "sequence_length": 681, "latency": 1.646151065826416, "success": true, "timestamp": 1748476246.6621583, "batch_id": "batch_5", "batch_index": 4, "processing_mode": "sequential"}, {"request_id": "req_64", "context_hash": "-5893263423773287004", "sequence_length": 20999, "latency": 2.405386447906494, "success": true, "timestamp": 1748476248.3083308, "batch_id": "batch_5", "batch_index": 4, "processing_mode": "sequential"}, {"request_id": "req_67", "context_hash": "-8570604672373697648", "sequence_length": 703, "latency": 1.6405541896820068, "success": true, "timestamp": 1748476250.7137506, "batch_id": "batch_5", "batch_index": 4, "processing_mode": "sequential"}, {"request_id": "req_71", "context_hash": "-7323124880561240370", "sequence_length": 907, "latency": 1.6516427993774414, "success": true, "timestamp": 1748476252.354334, "batch_id": "batch_5", "batch_index": 4, "processing_mode": "sequential"}, {"request_id": "req_68", "context_hash": "-7390988619282666577", "sequence_length": 607, "latency": 1.643472671508789, "success": true, "timestamp": 1748476254.0060031, "batch_id": "batch_5", "batch_index": 4, "processing_mode": "sequential"}, {"request_id": "req_63", "context_hash": "-7809262960343837230", "sequence_length": 909, "latency": 1.6483936309814453, "success": true, "timestamp": 1748476255.6495032, "batch_id": "batch_5", "batch_index": 4, "processing_mode": "sequential"}, {"request_id": "req_70", "context_hash": "-7766395146614640456", "sequence_length": 810, "latency": 1.6480035781860352, "success": true, "timestamp": 1748476257.2979212, "batch_id": "batch_5", "batch_index": 4, "processing_mode": "sequential"}, {"request_id": "req_69", "context_hash": "-8578968415085925033", "sequence_length": 664, "latency": 1.6433649063110352, "success": true, "timestamp": 1748476258.9459565, "batch_id": "batch_5", "batch_index": 4, "processing_mode": "sequential"}]}
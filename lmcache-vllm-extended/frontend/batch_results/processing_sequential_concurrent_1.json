{"experiment_config": {"num_batches": 5, "batch_size": 15, "repeat_ratio": 0.4, "diversity_level": 0.7, "processing_mode": "sequential", "max_concurrent": null}, "metrics": {"total_requests": 75, "successful_requests": 75, "failed_requests": 0, "average_latency": 1.7073726018269857, "total_latency": 128.05294513702393, "throughput": 0.5856952366050288}, "detailed_results": [{"request_id": "req_5", "context_hash": "4761470698806686381", "sequence_length": 669, "latency": 1.6767528057098389, "success": true, "timestamp": 1748476583.7914798, "batch_id": "batch_1", "batch_index": 0, "processing_mode": "sequential"}, {"request_id": "req_9", "context_hash": "-544989456438258722", "sequence_length": 680, "latency": 1.6339936256408691, "success": true, "timestamp": 1748476585.4682848, "batch_id": "batch_1", "batch_index": 0, "processing_mode": "sequential"}, {"request_id": "req_10", "context_hash": "8432233586567767916", "sequence_length": 701, "latency": 1.6355924606323242, "success": true, "timestamp": 1748476587.102322, "batch_id": "batch_1", "batch_index": 0, "processing_mode": "sequential"}, {"request_id": "req_4", "context_hash": "8432233586567767916", "sequence_length": 702, "latency": 1.6367626190185547, "success": true, "timestamp": 1748476588.7379515, "batch_id": "batch_1", "batch_index": 0, "processing_mode": "sequential"}, {"request_id": "req_11", "context_hash": "8432233586567767916", "sequence_length": 703, "latency": 1.6369540691375732, "success": true, "timestamp": 1748476590.3747463, "batch_id": "batch_1", "batch_index": 0, "processing_mode": "sequential"}, {"request_id": "req_8", "context_hash": "7376429434202549356", "sequence_length": 792, "latency": 1.6402289867401123, "success": true, "timestamp": 1748476592.0117342, "batch_id": "batch_1", "batch_index": 0, "processing_mode": "sequential"}, {"request_id": "req_15", "context_hash": "7376429434202549356", "sequence_length": 792, "latency": 1.6414659023284912, "success": true, "timestamp": 1748476593.6519928, "batch_id": "batch_1", "batch_index": 0, "processing_mode": "sequential"}, {"request_id": "req_2", "context_hash": "3854463334651596937", "sequence_length": 832, "latency": 1.640688180923462, "success": true, "timestamp": 1748476595.2934859, "batch_id": "batch_1", "batch_index": 0, "processing_mode": "sequential"}, {"request_id": "req_7", "context_hash": "8725621198975087011", "sequence_length": 905, "latency": 1.6405353546142578, "success": true, "timestamp": 1748476596.9341967, "batch_id": "batch_1", "batch_index": 0, "processing_mode": "sequential"}, {"request_id": "req_1", "context_hash": "-8371489123069024731", "sequence_length": 911, "latency": 1.6450088024139404, "success": true, "timestamp": 1748476598.5747557, "batch_id": "batch_1", "batch_index": 0, "processing_mode": "sequential"}, {"request_id": "req_14", "context_hash": "-8371489123069024731", "sequence_length": 912, "latency": 1.6445021629333496, "success": true, "timestamp": 1748476600.2197878, "batch_id": "batch_1", "batch_index": 0, "processing_mode": "sequential"}, {"request_id": "req_3", "context_hash": "-1475009954352875932", "sequence_length": 923, "latency": 1.6428637504577637, "success": true, "timestamp": 1748476601.8643184, "batch_id": "batch_1", "batch_index": 0, "processing_mode": "sequential"}, {"request_id": "req_12", "context_hash": "-1475009954352875932", "sequence_length": 925, "latency": 1.6418054103851318, "success": true, "timestamp": 1748476603.507208, "batch_id": "batch_1", "batch_index": 0, "processing_mode": "sequential"}, {"request_id": "req_13", "context_hash": "-2639202626256972259", "sequence_length": 20998, "latency": 2.3351309299468994, "success": true, "timestamp": 1748476605.14904, "batch_id": "batch_1", "batch_index": 0, "processing_mode": "sequential"}, {"request_id": "req_6", "context_hash": "-2639202626256972259", "sequence_length": 20999, "latency": 2.3200864791870117, "success": true, "timestamp": 1748476607.4842038, "batch_id": "batch_1", "batch_index": 0, "processing_mode": "sequential"}, {"request_id": "req_25", "context_hash": "-2639202626256972259", "sequence_length": 20999, "latency": 2.3331356048583984, "success": true, "timestamp": 1748476609.8048599, "batch_id": "batch_2", "batch_index": 1, "processing_mode": "sequential"}, {"request_id": "req_18", "context_hash": "-2639202626256972259", "sequence_length": 21001, "latency": 2.3374741077423096, "success": true, "timestamp": 1748476612.1380353, "batch_id": "batch_2", "batch_index": 1, "processing_mode": "sequential"}, {"request_id": "req_29", "context_hash": "-2639202626256972259", "sequence_length": 21001, "latency": 2.31653094291687, "success": true, "timestamp": 1748476614.4755483, "batch_id": "batch_2", "batch_index": 1, "processing_mode": "sequential"}, {"request_id": "req_22", "context_hash": "-8371489123069024731", "sequence_length": 910, "latency": 1.638770341873169, "success": true, "timestamp": 1748476616.7921157, "batch_id": "batch_2", "batch_index": 1, "processing_mode": "sequential"}, {"request_id": "req_28", "context_hash": "-8371489123069024731", "sequence_length": 910, "latency": 1.6450798511505127, "success": true, "timestamp": 1748476618.4309187, "batch_id": "batch_2", "batch_index": 1, "processing_mode": "sequential"}, {"request_id": "req_24", "context_hash": "-1475009954352875932", "sequence_length": 922, "latency": 1.6436121463775635, "success": true, "timestamp": 1748476620.0760334, "batch_id": "batch_2", "batch_index": 1, "processing_mode": "sequential"}, {"request_id": "req_21", "context_hash": "3854463334651596937", "sequence_length": 832, "latency": 1.6419310569763184, "success": true, "timestamp": 1748476621.7196763, "batch_id": "batch_2", "batch_index": 1, "processing_mode": "sequential"}, {"request_id": "req_30", "context_hash": "5905358039202356043", "sequence_length": 610, "latency": 1.636662483215332, "success": true, "timestamp": 1748476623.361633, "batch_id": "batch_2", "batch_index": 1, "processing_mode": "sequential"}, {"request_id": "req_23", "context_hash": "5905358039202356043", "sequence_length": 610, "latency": 1.639512062072754, "success": true, "timestamp": 1748476624.998323, "batch_id": "batch_2", "batch_index": 1, "processing_mode": "sequential"}, {"request_id": "req_20", "context_hash": "-3857242934652317486", "sequence_length": 764, "latency": 1.6406652927398682, "success": true, "timestamp": 1748476626.6378736, "batch_id": "batch_2", "batch_index": 1, "processing_mode": "sequential"}, {"request_id": "req_27", "context_hash": "-3857242934652317486", "sequence_length": 764, "latency": 1.642920732498169, "success": true, "timestamp": 1748476628.278569, "batch_id": "batch_2", "batch_index": 1, "processing_mode": "sequential"}, {"request_id": "req_26", "context_hash": "-3857242934652317486", "sequence_length": 764, "latency": 1.6425492763519287, "success": true, "timestamp": 1748476629.9215274, "batch_id": "batch_2", "batch_index": 1, "processing_mode": "sequential"}, {"request_id": "req_19", "context_hash": "3532558079807027209", "sequence_length": 808, "latency": 1.6425609588623047, "success": true, "timestamp": 1748476631.564108, "batch_id": "batch_2", "batch_index": 1, "processing_mode": "sequential"}, {"request_id": "req_16", "context_hash": "-2234298079826358899", "sequence_length": 885, "latency": 1.6446647644042969, "success": true, "timestamp": 1748476633.2066932, "batch_id": "batch_2", "batch_index": 1, "processing_mode": "sequential"}, {"request_id": "req_17", "context_hash": "-1851340700922038712", "sequence_length": 893, "latency": 1.6441535949707031, "success": true, "timestamp": 1748476634.8513846, "batch_id": "batch_2", "batch_index": 1, "processing_mode": "sequential"}, {"request_id": "req_41", "context_hash": "4761470698806686381", "sequence_length": 667, "latency": 1.638159990310669, "success": true, "timestamp": 1748476636.4959075, "batch_id": "batch_3", "batch_index": 2, "processing_mode": "sequential"}, {"request_id": "req_31", "context_hash": "4761470698806686381", "sequence_length": 668, "latency": 1.6384899616241455, "success": true, "timestamp": 1748476638.1341019, "batch_id": "batch_3", "batch_index": 2, "processing_mode": "sequential"}, {"request_id": "req_43", "context_hash": "4761470698806686381", "sequence_length": 669, "latency": 1.6405723094940186, "success": true, "timestamp": 1748476639.7726254, "batch_id": "batch_3", "batch_index": 2, "processing_mode": "sequential"}, {"request_id": "req_40", "context_hash": "4761470698806686381", "sequence_length": 669, "latency": 1.6414861679077148, "success": true, "timestamp": 1748476641.413227, "batch_id": "batch_3", "batch_index": 2, "processing_mode": "sequential"}, {"request_id": "req_35", "context_hash": "8432233586567767916", "sequence_length": 702, "latency": 1.6431663036346436, "success": true, "timestamp": 1748476643.0547433, "batch_id": "batch_3", "batch_index": 2, "processing_mode": "sequential"}, {"request_id": "req_45", "context_hash": "8432233586567767916", "sequence_length": 703, "latency": 1.6430821418762207, "success": true, "timestamp": 1748476644.6979396, "batch_id": "batch_3", "batch_index": 2, "processing_mode": "sequential"}, {"request_id": "req_36", "context_hash": "3854463334651596937", "sequence_length": 830, "latency": 1.640528917312622, "success": true, "timestamp": 1748476646.3410506, "batch_id": "batch_3", "batch_index": 2, "processing_mode": "sequential"}, {"request_id": "req_42", "context_hash": "3854463334651596937", "sequence_length": 832, "latency": 1.6431231498718262, "success": true, "timestamp": 1748476647.9816067, "batch_id": "batch_3", "batch_index": 2, "processing_mode": "sequential"}, {"request_id": "req_34", "context_hash": "-8371489123069024731", "sequence_length": 911, "latency": 1.6458923816680908, "success": true, "timestamp": 1748476649.6247575, "batch_id": "batch_3", "batch_index": 2, "processing_mode": "sequential"}, {"request_id": "req_44", "context_hash": "-8371489123069024731", "sequence_length": 911, "latency": 1.6455104351043701, "success": true, "timestamp": 1748476651.2706776, "batch_id": "batch_3", "batch_index": 2, "processing_mode": "sequential"}, {"request_id": "req_32", "context_hash": "-1851340700922038712", "sequence_length": 891, "latency": 1.645981788635254, "success": true, "timestamp": 1748476652.9162145, "batch_id": "batch_3", "batch_index": 2, "processing_mode": "sequential"}, {"request_id": "req_33", "context_hash": "-1475009954352875932", "sequence_length": 922, "latency": 1.6450109481811523, "success": true, "timestamp": 1748476654.562224, "batch_id": "batch_3", "batch_index": 2, "processing_mode": "sequential"}, {"request_id": "req_38", "context_hash": "-3857242934652317486", "sequence_length": 764, "latency": 1.6452150344848633, "success": true, "timestamp": 1748476656.2072642, "batch_id": "batch_3", "batch_index": 2, "processing_mode": "sequential"}, {"request_id": "req_39", "context_hash": "3532558079807027209", "sequence_length": 810, "latency": 1.6407032012939453, "success": true, "timestamp": 1748476657.852504, "batch_id": "batch_3", "batch_index": 2, "processing_mode": "sequential"}, {"request_id": "req_37", "context_hash": "-6195994867532386088", "sequence_length": 663, "latency": 1.643049955368042, "success": true, "timestamp": 1748476659.4932342, "batch_id": "batch_3", "batch_index": 2, "processing_mode": "sequential"}, {"request_id": "req_59", "context_hash": "-544989456438258722", "sequence_length": 679, "latency": 1.6429429054260254, "success": true, "timestamp": 1748476661.1365511, "batch_id": "batch_4", "batch_index": 3, "processing_mode": "sequential"}, {"request_id": "req_53", "context_hash": "-544989456438258722", "sequence_length": 682, "latency": 1.6398775577545166, "success": true, "timestamp": 1748476662.7795324, "batch_id": "batch_4", "batch_index": 3, "processing_mode": "sequential"}, {"request_id": "req_46", "context_hash": "-1851340700922038712", "sequence_length": 891, "latency": 1.6416728496551514, "success": true, "timestamp": 1748476664.4194453, "batch_id": "batch_4", "batch_index": 3, "processing_mode": "sequential"}, {"request_id": "req_55", "context_hash": "-1851340700922038712", "sequence_length": 894, "latency": 1.644622564315796, "success": true, "timestamp": 1748476666.061151, "batch_id": "batch_4", "batch_index": 3, "processing_mode": "sequential"}, {"request_id": "req_60", "context_hash": "-1475009954352875932", "sequence_length": 922, "latency": 1.6468515396118164, "success": true, "timestamp": 1748476667.7058065, "batch_id": "batch_4", "batch_index": 3, "processing_mode": "sequential"}, {"request_id": "req_54", "context_hash": "-1475009954352875932", "sequence_length": 925, "latency": 1.6458122730255127, "success": true, "timestamp": 1748476669.3526902, "batch_id": "batch_4", "batch_index": 3, "processing_mode": "sequential"}, {"request_id": "req_58", "context_hash": "-3857242934652317486", "sequence_length": 765, "latency": 1.6454439163208008, "success": true, "timestamp": 1748476670.9985313, "batch_id": "batch_4", "batch_index": 3, "processing_mode": "sequential"}, {"request_id": "req_49", "context_hash": "-3857242934652317486", "sequence_length": 766, "latency": 1.642582893371582, "success": true, "timestamp": 1748476672.6440103, "batch_id": "batch_4", "batch_index": 3, "processing_mode": "sequential"}, {"request_id": "req_57", "context_hash": "8725621198975087011", "sequence_length": 905, "latency": 1.6464190483093262, "success": true, "timestamp": 1748476674.2866259, "batch_id": "batch_4", "batch_index": 3, "processing_mode": "sequential"}, {"request_id": "req_48", "context_hash": "8725621198975087011", "sequence_length": 907, "latency": 1.6448752880096436, "success": true, "timestamp": 1748476675.9330797, "batch_id": "batch_4", "batch_index": 3, "processing_mode": "sequential"}, {"request_id": "req_56", "context_hash": "-6195994867532386088", "sequence_length": 664, "latency": 1.6425883769989014, "success": true, "timestamp": 1748476677.577995, "batch_id": "batch_4", "batch_index": 3, "processing_mode": "sequential"}, {"request_id": "req_51", "context_hash": "-6195994867532386088", "sequence_length": 666, "latency": 1.6437015533447266, "success": true, "timestamp": 1748476679.2206197, "batch_id": "batch_4", "batch_index": 3, "processing_mode": "sequential"}, {"request_id": "req_47", "context_hash": "5905358039202356043", "sequence_length": 610, "latency": 1.6378722190856934, "success": true, "timestamp": 1748476680.8643615, "batch_id": "batch_4", "batch_index": 3, "processing_mode": "sequential"}, {"request_id": "req_52", "context_hash": "7376429434202549356", "sequence_length": 791, "latency": 1.6460931301116943, "success": true, "timestamp": 1748476682.5022738, "batch_id": "batch_4", "batch_index": 3, "processing_mode": "sequential"}, {"request_id": "req_50", "context_hash": "-2639202626256972259", "sequence_length": 20999, "latency": 2.373689889907837, "success": true, "timestamp": 1748476684.1484053, "batch_id": "batch_4", "batch_index": 3, "processing_mode": "sequential"}, {"request_id": "req_74", "context_hash": "4761470698806686381", "sequence_length": 667, "latency": 1.6371569633483887, "success": true, "timestamp": 1748476686.5223722, "batch_id": "batch_5", "batch_index": 4, "processing_mode": "sequential"}, {"request_id": "req_62", "context_hash": "4761470698806686381", "sequence_length": 668, "latency": 1.6419894695281982, "success": true, "timestamp": 1748476688.159564, "batch_id": "batch_5", "batch_index": 4, "processing_mode": "sequential"}, {"request_id": "req_71", "context_hash": "4761470698806686381", "sequence_length": 668, "latency": 1.6414523124694824, "success": true, "timestamp": 1748476689.8015833, "batch_id": "batch_5", "batch_index": 4, "processing_mode": "sequential"}, {"request_id": "req_61", "context_hash": "8725621198975087011", "sequence_length": 904, "latency": 1.6430363655090332, "success": true, "timestamp": 1748476691.4430616, "batch_id": "batch_5", "batch_index": 4, "processing_mode": "sequential"}, {"request_id": "req_70", "context_hash": "8725621198975087011", "sequence_length": 907, "latency": 1.6472952365875244, "success": true, "timestamp": 1748476693.0861225, "batch_id": "batch_5", "batch_index": 4, "processing_mode": "sequential"}, {"request_id": "req_72", "context_hash": "8725621198975087011", "sequence_length": 907, "latency": 1.6451129913330078, "success": true, "timestamp": 1748476694.7334416, "batch_id": "batch_5", "batch_index": 4, "processing_mode": "sequential"}, {"request_id": "req_64", "context_hash": "3532558079807027209", "sequence_length": 810, "latency": 1.6456408500671387, "success": true, "timestamp": 1748476696.378578, "batch_id": "batch_5", "batch_index": 4, "processing_mode": "sequential"}, {"request_id": "req_73", "context_hash": "3532558079807027209", "sequence_length": 810, "latency": 1.6483948230743408, "success": true, "timestamp": 1748476698.024242, "batch_id": "batch_5", "batch_index": 4, "processing_mode": "sequential"}, {"request_id": "req_75", "context_hash": "-2234298079826358899", "sequence_length": 883, "latency": 1.6458823680877686, "success": true, "timestamp": 1748476699.672659, "batch_id": "batch_5", "batch_index": 4, "processing_mode": "sequential"}, {"request_id": "req_69", "context_hash": "-2234298079826358899", "sequence_length": 885, "latency": 1.642169713973999, "success": true, "timestamp": 1748476701.3185701, "batch_id": "batch_5", "batch_index": 4, "processing_mode": "sequential"}, {"request_id": "req_67", "context_hash": "8432233586567767916", "sequence_length": 700, "latency": 1.6405396461486816, "success": true, "timestamp": 1748476702.9607646, "batch_id": "batch_5", "batch_index": 4, "processing_mode": "sequential"}, {"request_id": "req_63", "context_hash": "-2639202626256972259", "sequence_length": 21000, "latency": 2.31923770904541, "success": true, "timestamp": 1748476704.601339, "batch_id": "batch_5", "batch_index": 4, "processing_mode": "sequential"}, {"request_id": "req_65", "context_hash": "-8371489123069024731", "sequence_length": 912, "latency": 1.6407694816589355, "success": true, "timestamp": 1748476706.9206092, "batch_id": "batch_5", "batch_index": 4, "processing_mode": "sequential"}, {"request_id": "req_68", "context_hash": "-1851340700922038712", "sequence_length": 892, "latency": 1.6435904502868652, "success": true, "timestamp": 1748476708.5614073, "batch_id": "batch_5", "batch_index": 4, "processing_mode": "sequential"}, {"request_id": "req_66", "context_hash": "-6195994867532386088", "sequence_length": 665, "latency": 1.6430633068084717, "success": true, "timestamp": 1748476710.2050383, "batch_id": "batch_5", "batch_index": 4, "processing_mode": "sequential"}]}
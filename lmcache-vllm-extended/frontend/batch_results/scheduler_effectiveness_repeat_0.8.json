{"experiment_config": {"num_batches": 5, "batch_size": 10, "repeat_ratio": 0.8, "diversity_level": 1.0, "processing_mode": "sequential", "max_concurrent": null}, "metrics": {"total_requests": 50, "successful_requests": 50, "failed_requests": 0, "average_latency": 0.7289432573318482, "total_latency": 36.44716286659241, "throughput": 1.3718488921350356}, "detailed_results": [{"request_id": "req_2", "context_hash": "-5929608903871432148", "sequence_length": 664, "latency": 0.29029130935668945, "success": true, "timestamp": 1748476042.4367971, "batch_id": "batch_1", "batch_index": 0, "processing_mode": "sequential"}, {"request_id": "req_9", "context_hash": "-5929608903871432148", "sequence_length": 666, "latency": 0.21974420547485352, "success": true, "timestamp": 1748476042.727122, "batch_id": "batch_1", "batch_index": 0, "processing_mode": "sequential"}, {"request_id": "req_8", "context_hash": "-5929608903871432148", "sequence_length": 666, "latency": 0.2192530632019043, "success": true, "timestamp": 1748476042.9468915, "batch_id": "batch_1", "batch_index": 0, "processing_mode": "sequential"}, {"request_id": "req_5", "context_hash": "7463781651125507186", "sequence_length": 829, "latency": 0.7730693817138672, "success": true, "timestamp": 1748476043.1661634, "batch_id": "batch_1", "batch_index": 0, "processing_mode": "sequential"}, {"request_id": "req_6", "context_hash": "7463781651125507186", "sequence_length": 830, "latency": 0.756598711013794, "success": true, "timestamp": 1748476043.9392557, "batch_id": "batch_1", "batch_index": 0, "processing_mode": "sequential"}, {"request_id": "req_10", "context_hash": "7463781651125507186", "sequence_length": 831, "latency": 0.7573761940002441, "success": true, "timestamp": 1748476044.695873, "batch_id": "batch_1", "batch_index": 0, "processing_mode": "sequential"}, {"request_id": "req_1", "context_hash": "7463781651125507186", "sequence_length": 831, "latency": 0.7410171031951904, "success": true, "timestamp": 1748476045.4532669, "batch_id": "batch_1", "batch_index": 0, "processing_mode": "sequential"}, {"request_id": "req_7", "context_hash": "7463781651125507186", "sequence_length": 832, "latency": 0.724761962890625, "success": true, "timestamp": 1748476046.194304, "batch_id": "batch_1", "batch_index": 0, "processing_mode": "sequential"}, {"request_id": "req_4", "context_hash": "7463781651125507186", "sequence_length": 832, "latency": 0.7410356998443604, "success": true, "timestamp": 1748476046.9190836, "batch_id": "batch_1", "batch_index": 0, "processing_mode": "sequential"}, {"request_id": "req_3", "context_hash": "7463781651125507186", "sequence_length": 832, "latency": 0.7253396511077881, "success": true, "timestamp": 1748476047.6601367, "batch_id": "batch_1", "batch_index": 0, "processing_mode": "sequential"}, {"request_id": "req_20", "context_hash": "8889704462012247241", "sequence_length": 882, "latency": 0.14056658744812012, "success": true, "timestamp": 1748476048.3857896, "batch_id": "batch_2", "batch_index": 1, "processing_mode": "sequential"}, {"request_id": "req_12", "context_hash": "8889704462012247241", "sequence_length": 884, "latency": 0.10833168029785156, "success": true, "timestamp": 1748476048.5263762, "batch_id": "batch_2", "batch_index": 1, "processing_mode": "sequential"}, {"request_id": "req_18", "context_hash": "8889704462012247241", "sequence_length": 884, "latency": 0.12350654602050781, "success": true, "timestamp": 1748476048.6347249, "batch_id": "batch_2", "batch_index": 1, "processing_mode": "sequential"}, {"request_id": "req_16", "context_hash": "8889704462012247241", "sequence_length": 885, "latency": 0.09126782417297363, "success": true, "timestamp": 1748476048.7582464, "batch_id": "batch_2", "batch_index": 1, "processing_mode": "sequential"}, {"request_id": "req_19", "context_hash": "-8614966080862659398", "sequence_length": 909, "latency": 0.07648968696594238, "success": true, "timestamp": 1748476048.8495302, "batch_id": "batch_2", "batch_index": 1, "processing_mode": "sequential"}, {"request_id": "req_11", "context_hash": "-8614966080862659398", "sequence_length": 910, "latency": 1.6525475978851318, "success": true, "timestamp": 1748476048.9260345, "batch_id": "batch_2", "batch_index": 1, "processing_mode": "sequential"}, {"request_id": "req_14", "context_hash": "-8614966080862659398", "sequence_length": 912, "latency": 0.043607234954833984, "success": true, "timestamp": 1748476050.5786023, "batch_id": "batch_2", "batch_index": 1, "processing_mode": "sequential"}, {"request_id": "req_15", "context_hash": "-8614966080862659398", "sequence_length": 912, "latency": 0.04240751266479492, "success": true, "timestamp": 1748476050.6222243, "batch_id": "batch_2", "batch_index": 1, "processing_mode": "sequential"}, {"request_id": "req_13", "context_hash": "-8614966080862659398", "sequence_length": 912, "latency": 0.05837130546569824, "success": true, "timestamp": 1748476050.6646461, "batch_id": "batch_2", "batch_index": 1, "processing_mode": "sequential"}, {"request_id": "req_17", "context_hash": "-8614966080862659398", "sequence_length": 912, "latency": 1.6527862548828125, "success": true, "timestamp": 1748476050.7230308, "batch_id": "batch_2", "batch_index": 1, "processing_mode": "sequential"}, {"request_id": "req_25", "context_hash": "7276504430085208112", "sequence_length": 891, "latency": 1.6528825759887695, "success": true, "timestamp": 1748476052.376116, "batch_id": "batch_3", "batch_index": 2, "processing_mode": "sequential"}, {"request_id": "req_21", "context_hash": "7276504430085208112", "sequence_length": 892, "latency": 0.09161019325256348, "success": true, "timestamp": 1748476054.0290215, "batch_id": "batch_3", "batch_index": 2, "processing_mode": "sequential"}, {"request_id": "req_26", "context_hash": "7276504430085208112", "sequence_length": 892, "latency": 1.6534368991851807, "success": true, "timestamp": 1748476054.1206496, "batch_id": "batch_3", "batch_index": 2, "processing_mode": "sequential"}, {"request_id": "req_27", "context_hash": "7276504430085208112", "sequence_length": 893, "latency": 0.05985069274902344, "success": true, "timestamp": 1748476055.7741086, "batch_id": "batch_3", "batch_index": 2, "processing_mode": "sequential"}, {"request_id": "req_24", "context_hash": "7276504430085208112", "sequence_length": 894, "latency": 1.6521806716918945, "success": true, "timestamp": 1748476055.8339775, "batch_id": "batch_3", "batch_index": 2, "processing_mode": "sequential"}, {"request_id": "req_29", "context_hash": "4124270617424690497", "sequence_length": 922, "latency": 1.6535253524780273, "success": true, "timestamp": 1748476057.4861782, "batch_id": "batch_3", "batch_index": 2, "processing_mode": "sequential"}, {"request_id": "req_28", "context_hash": "4124270617424690497", "sequence_length": 922, "latency": 1.6549742221832275, "success": true, "timestamp": 1748476059.1397283, "batch_id": "batch_3", "batch_index": 2, "processing_mode": "sequential"}, {"request_id": "req_30", "context_hash": "4124270617424690497", "sequence_length": 924, "latency": 1.6545557975769043, "success": true, "timestamp": 1748476060.7947252, "batch_id": "batch_3", "batch_index": 2, "processing_mode": "sequential"}, {"request_id": "req_23", "context_hash": "4124270617424690497", "sequence_length": 925, "latency": 1.656412124633789, "success": true, "timestamp": 1748476062.4493186, "batch_id": "batch_3", "batch_index": 2, "processing_mode": "sequential"}, {"request_id": "req_22", "context_hash": "4124270617424690497", "sequence_length": 925, "latency": 1.6554090976715088, "success": true, "timestamp": 1748476064.105756, "batch_id": "batch_3", "batch_index": 2, "processing_mode": "sequential"}, {"request_id": "req_32", "context_hash": "-2191423892468461316", "sequence_length": 607, "latency": 1.1122405529022217, "success": true, "timestamp": 1748476065.7614677, "batch_id": "batch_4", "batch_index": 3, "processing_mode": "sequential"}, {"request_id": "req_34", "context_hash": "-2191423892468461316", "sequence_length": 609, "latency": 0.25160861015319824, "success": true, "timestamp": 1748476066.8737428, "batch_id": "batch_4", "batch_index": 3, "processing_mode": "sequential"}, {"request_id": "req_37", "context_hash": "-2191423892468461316", "sequence_length": 610, "latency": 0.5402126312255859, "success": true, "timestamp": 1748476067.1253812, "batch_id": "batch_4", "batch_index": 3, "processing_mode": "sequential"}, {"request_id": "req_40", "context_hash": "6287053221092833919", "sequence_length": 700, "latency": 0.042197465896606445, "success": true, "timestamp": 1748476067.6656191, "batch_id": "batch_4", "batch_index": 3, "processing_mode": "sequential"}, {"request_id": "req_36", "context_hash": "6287053221092833919", "sequence_length": 701, "latency": 0.7718424797058105, "success": true, "timestamp": 1748476067.707835, "batch_id": "batch_4", "batch_index": 3, "processing_mode": "sequential"}, {"request_id": "req_33", "context_hash": "6287053221092833919", "sequence_length": 701, "latency": 0.04159235954284668, "success": true, "timestamp": 1748476068.4796977, "batch_id": "batch_4", "batch_index": 3, "processing_mode": "sequential"}, {"request_id": "req_35", "context_hash": "6287053221092833919", "sequence_length": 702, "latency": 0.7871580123901367, "success": true, "timestamp": 1748476068.5213075, "batch_id": "batch_4", "batch_index": 3, "processing_mode": "sequential"}, {"request_id": "req_38", "context_hash": "6287053221092833919", "sequence_length": 702, "latency": 0.8046243190765381, "success": true, "timestamp": 1748476069.308483, "batch_id": "batch_4", "batch_index": 3, "processing_mode": "sequential"}, {"request_id": "req_39", "context_hash": "6287053221092833919", "sequence_length": 703, "latency": 0.7883584499359131, "success": true, "timestamp": 1748476070.1131222, "batch_id": "batch_4", "batch_index": 3, "processing_mode": "sequential"}, {"request_id": "req_31", "context_hash": "6287053221092833919", "sequence_length": 703, "latency": 0.7724988460540771, "success": true, "timestamp": 1748476070.9014974, "batch_id": "batch_4", "batch_index": 3, "processing_mode": "sequential"}, {"request_id": "req_42", "context_hash": "-2191423892468461316", "sequence_length": 607, "latency": 0.6087419986724854, "success": true, "timestamp": 1748476071.674282, "batch_id": "batch_5", "batch_index": 4, "processing_mode": "sequential"}, {"request_id": "req_50", "context_hash": "-2191423892468461316", "sequence_length": 608, "latency": 1.1449339389801025, "success": true, "timestamp": 1748476072.2830443, "batch_id": "batch_5", "batch_index": 4, "processing_mode": "sequential"}, {"request_id": "req_48", "context_hash": "-2191423892468461316", "sequence_length": 608, "latency": 0.46255922317504883, "success": true, "timestamp": 1748476073.428005, "batch_id": "batch_5", "batch_index": 4, "processing_mode": "sequential"}, {"request_id": "req_45", "context_hash": "-2191423892468461316", "sequence_length": 609, "latency": 0.42885541915893555, "success": true, "timestamp": 1748476073.890585, "batch_id": "batch_5", "batch_index": 4, "processing_mode": "sequential"}, {"request_id": "req_46", "context_hash": "-2191423892468461316", "sequence_length": 610, "latency": 0.821298360824585, "success": true, "timestamp": 1748476074.3194625, "batch_id": "batch_5", "batch_index": 4, "processing_mode": "sequential"}, {"request_id": "req_41", "context_hash": "-543761367042884529", "sequence_length": 905, "latency": 1.654158592224121, "success": true, "timestamp": 1748476075.1407802, "batch_id": "batch_5", "batch_index": 4, "processing_mode": "sequential"}, {"request_id": "req_44", "context_hash": "-543761367042884529", "sequence_length": 905, "latency": 1.6527037620544434, "success": true, "timestamp": 1748476076.7949703, "batch_id": "batch_5", "batch_index": 4, "processing_mode": "sequential"}, {"request_id": "req_43", "context_hash": "-543761367042884529", "sequence_length": 906, "latency": 0.14113116264343262, "success": true, "timestamp": 1748476078.447705, "batch_id": "batch_5", "batch_index": 4, "processing_mode": "sequential"}, {"request_id": "req_49", "context_hash": "-543761367042884529", "sequence_length": 906, "latency": 0.15641546249389648, "success": true, "timestamp": 1748476078.5888615, "batch_id": "batch_5", "batch_index": 4, "processing_mode": "sequential"}, {"request_id": "req_47", "context_hash": "-543761367042884529", "sequence_length": 906, "latency": 0.1408240795135498, "success": true, "timestamp": 1748476078.7452967, "batch_id": "batch_5", "batch_index": 4, "processing_mode": "sequential"}]}
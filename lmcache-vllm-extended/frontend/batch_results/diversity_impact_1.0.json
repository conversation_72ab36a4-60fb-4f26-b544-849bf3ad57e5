{"experiment_config": {"num_batches": 5, "batch_size": 15, "repeat_ratio": 0.3, "diversity_level": 1.0, "processing_mode": "sequential", "max_concurrent": null}, "metrics": {"total_requests": 75, "successful_requests": 75, "failed_requests": 0, "average_latency": 1.6321622371673583, "total_latency": 122.41216778755188, "throughput": 0.6126841910860006}, "detailed_results": [{"request_id": "req_10", "context_hash": "-2056895311811022652", "sequence_length": 610, "latency": 1.6776435375213623, "success": true, "timestamp": 1748476265.9709916, "batch_id": "batch_1", "batch_index": 0, "processing_mode": "sequential"}, {"request_id": "req_1", "context_hash": "6499229912946433423", "sequence_length": 663, "latency": 1.6413795948028564, "success": true, "timestamp": 1748476267.648704, "batch_id": "batch_1", "batch_index": 0, "processing_mode": "sequential"}, {"request_id": "req_12", "context_hash": "6499229912946433423", "sequence_length": 663, "latency": 1.6419293880462646, "success": true, "timestamp": 1748476269.2901165, "batch_id": "batch_1", "batch_index": 0, "processing_mode": "sequential"}, {"request_id": "req_15", "context_hash": "6499229912946433423", "sequence_length": 666, "latency": 1.6409509181976318, "success": true, "timestamp": 1748476270.9320743, "batch_id": "batch_1", "batch_index": 0, "processing_mode": "sequential"}, {"request_id": "req_5", "context_hash": "-6118425561090981083", "sequence_length": 669, "latency": 1.6437578201293945, "success": true, "timestamp": 1748476272.5730536, "batch_id": "batch_1", "batch_index": 0, "processing_mode": "sequential"}, {"request_id": "req_3", "context_hash": "7913960499510928454", "sequence_length": 680, "latency": 1.6446075439453125, "success": true, "timestamp": 1748476274.2168381, "batch_id": "batch_1", "batch_index": 0, "processing_mode": "sequential"}, {"request_id": "req_11", "context_hash": "-9049639821994633092", "sequence_length": 702, "latency": 1.6397228240966797, "success": true, "timestamp": 1748476275.8614733, "batch_id": "batch_1", "batch_index": 0, "processing_mode": "sequential"}, {"request_id": "req_9", "context_hash": "7899067936298576244", "sequence_length": 765, "latency": 1.6408143043518066, "success": true, "timestamp": 1748476277.5012212, "batch_id": "batch_1", "batch_index": 0, "processing_mode": "sequential"}, {"request_id": "req_14", "context_hash": "7899067936298576244", "sequence_length": 765, "latency": 1.6439268589019775, "success": true, "timestamp": 1748476279.1420577, "batch_id": "batch_1", "batch_index": 0, "processing_mode": "sequential"}, {"request_id": "req_13", "context_hash": "2982775370186856643", "sequence_length": 808, "latency": 1.6423392295837402, "success": true, "timestamp": 1748476280.7860045, "batch_id": "batch_1", "batch_index": 0, "processing_mode": "sequential"}, {"request_id": "req_8", "context_hash": "2982775370186856643", "sequence_length": 810, "latency": 1.6448798179626465, "success": true, "timestamp": 1748476282.4283638, "batch_id": "batch_1", "batch_index": 0, "processing_mode": "sequential"}, {"request_id": "req_6", "context_hash": "6025713090668406833", "sequence_length": 829, "latency": 1.646268606185913, "success": true, "timestamp": 1748476284.073265, "batch_id": "batch_1", "batch_index": 0, "processing_mode": "sequential"}, {"request_id": "req_7", "context_hash": "7314679619933683927", "sequence_length": 884, "latency": 1.6471092700958252, "success": true, "timestamp": 1748476285.7195547, "batch_id": "batch_1", "batch_index": 0, "processing_mode": "sequential"}, {"request_id": "req_2", "context_hash": "3427642709178807400", "sequence_length": 894, "latency": 1.6472434997558594, "success": true, "timestamp": 1748476287.3666852, "batch_id": "batch_1", "batch_index": 0, "processing_mode": "sequential"}, {"request_id": "req_4", "context_hash": "-432210505875366949", "sequence_length": 924, "latency": 1.6479771137237549, "success": true, "timestamp": 1748476289.0139496, "batch_id": "batch_1", "batch_index": 0, "processing_mode": "sequential"}, {"request_id": "req_22", "context_hash": "2982775370186856643", "sequence_length": 809, "latency": 1.6471312046051025, "success": true, "timestamp": 1748476290.6624734, "batch_id": "batch_2", "batch_index": 1, "processing_mode": "sequential"}, {"request_id": "req_27", "context_hash": "2982775370186856643", "sequence_length": 810, "latency": 1.646841049194336, "success": true, "timestamp": 1748476292.3096414, "batch_id": "batch_2", "batch_index": 1, "processing_mode": "sequential"}, {"request_id": "req_30", "context_hash": "2982775370186856643", "sequence_length": 810, "latency": 1.6444618701934814, "success": true, "timestamp": 1748476293.9565127, "batch_id": "batch_2", "batch_index": 1, "processing_mode": "sequential"}, {"request_id": "req_19", "context_hash": "3427642709178807400", "sequence_length": 892, "latency": 1.6461849212646484, "success": true, "timestamp": 1748476295.6009977, "batch_id": "batch_2", "batch_index": 1, "processing_mode": "sequential"}, {"request_id": "req_29", "context_hash": "3427642709178807400", "sequence_length": 893, "latency": 1.6461472511291504, "success": true, "timestamp": 1748476297.2472053, "batch_id": "batch_2", "batch_index": 1, "processing_mode": "sequential"}, {"request_id": "req_26", "context_hash": "7314679619933683927", "sequence_length": 883, "latency": 1.6449363231658936, "success": true, "timestamp": 1748476298.8933756, "batch_id": "batch_2", "batch_index": 1, "processing_mode": "sequential"}, {"request_id": "req_28", "context_hash": "7314679619933683927", "sequence_length": 884, "latency": 1.6454768180847168, "success": true, "timestamp": 1748476300.538339, "batch_id": "batch_2", "batch_index": 1, "processing_mode": "sequential"}, {"request_id": "req_17", "context_hash": "7913960499510928454", "sequence_length": 681, "latency": 1.6418836116790771, "success": true, "timestamp": 1748476302.183841, "batch_id": "batch_2", "batch_index": 1, "processing_mode": "sequential"}, {"request_id": "req_24", "context_hash": "-2056895311811022652", "sequence_length": 610, "latency": 1.6433477401733398, "success": true, "timestamp": 1748476303.8257499, "batch_id": "batch_2", "batch_index": 1, "processing_mode": "sequential"}, {"request_id": "req_16", "context_hash": "-9049639821994633092", "sequence_length": 703, "latency": 1.6431875228881836, "success": true, "timestamp": 1748476305.4691212, "batch_id": "batch_2", "batch_index": 1, "processing_mode": "sequential"}, {"request_id": "req_20", "context_hash": "7899067936298576244", "sequence_length": 765, "latency": 1.6453726291656494, "success": true, "timestamp": 1748476307.1123345, "batch_id": "batch_2", "batch_index": 1, "processing_mode": "sequential"}, {"request_id": "req_21", "context_hash": "990700289451623570", "sequence_length": 791, "latency": 1.6473569869995117, "success": true, "timestamp": 1748476308.7577274, "batch_id": "batch_2", "batch_index": 1, "processing_mode": "sequential"}, {"request_id": "req_18", "context_hash": "6282334307984525233", "sequence_length": 904, "latency": 1.6465067863464355, "success": true, "timestamp": 1748476310.4051092, "batch_id": "batch_2", "batch_index": 1, "processing_mode": "sequential"}, {"request_id": "req_25", "context_hash": "7827176742408056147", "sequence_length": 910, "latency": 1.646395206451416, "success": true, "timestamp": 1748476312.0516393, "batch_id": "batch_2", "batch_index": 1, "processing_mode": "sequential"}, {"request_id": "req_23", "context_hash": "5381588843390861417", "sequence_length": 20998, "latency": 2.3269100189208984, "success": true, "timestamp": 1748476313.6980577, "batch_id": "batch_2", "batch_index": 1, "processing_mode": "sequential"}, {"request_id": "req_40", "context_hash": "7314679619933683927", "sequence_length": 883, "latency": 1.6417889595031738, "success": true, "timestamp": 1748476316.0252776, "batch_id": "batch_3", "batch_index": 2, "processing_mode": "sequential"}, {"request_id": "req_45", "context_hash": "7314679619933683927", "sequence_length": 885, "latency": 1.6445744037628174, "success": true, "timestamp": 1748476317.6671042, "batch_id": "batch_3", "batch_index": 2, "processing_mode": "sequential"}, {"request_id": "req_42", "context_hash": "7314679619933683927", "sequence_length": 885, "latency": 1.6456825733184814, "success": true, "timestamp": 1748476319.3117096, "batch_id": "batch_3", "batch_index": 2, "processing_mode": "sequential"}, {"request_id": "req_39", "context_hash": "2982775370186856643", "sequence_length": 807, "latency": 1.647268533706665, "success": true, "timestamp": 1748476320.9574192, "batch_id": "batch_3", "batch_index": 2, "processing_mode": "sequential"}, {"request_id": "req_44", "context_hash": "2982775370186856643", "sequence_length": 808, "latency": 1.6468353271484375, "success": true, "timestamp": 1748476322.6047127, "batch_id": "batch_3", "batch_index": 2, "processing_mode": "sequential"}, {"request_id": "req_43", "context_hash": "7913960499510928454", "sequence_length": 682, "latency": 1.6427514553070068, "success": true, "timestamp": 1748476324.2515776, "batch_id": "batch_3", "batch_index": 2, "processing_mode": "sequential"}, {"request_id": "req_38", "context_hash": "7913960499510928454", "sequence_length": 682, "latency": 1.6443121433258057, "success": true, "timestamp": 1748476325.8943543, "batch_id": "batch_3", "batch_index": 2, "processing_mode": "sequential"}, {"request_id": "req_33", "context_hash": "990700289451623570", "sequence_length": 793, "latency": 1.6477196216583252, "success": true, "timestamp": 1748476327.5386975, "batch_id": "batch_3", "batch_index": 2, "processing_mode": "sequential"}, {"request_id": "req_41", "context_hash": "3427642709178807400", "sequence_length": 891, "latency": 1.6491179466247559, "success": true, "timestamp": 1748476329.1864467, "batch_id": "batch_3", "batch_index": 2, "processing_mode": "sequential"}, {"request_id": "req_31", "context_hash": "7899067936298576244", "sequence_length": 766, "latency": 1.6449425220489502, "success": true, "timestamp": 1748476330.8355935, "batch_id": "batch_3", "batch_index": 2, "processing_mode": "sequential"}, {"request_id": "req_37", "context_hash": "-9049639821994633092", "sequence_length": 701, "latency": 1.6445062160491943, "success": true, "timestamp": 1748476332.4805672, "batch_id": "batch_3", "batch_index": 2, "processing_mode": "sequential"}, {"request_id": "req_34", "context_hash": "-2056895311811022652", "sequence_length": 610, "latency": 1.6404621601104736, "success": true, "timestamp": 1748476334.1250992, "batch_id": "batch_3", "batch_index": 2, "processing_mode": "sequential"}, {"request_id": "req_36", "context_hash": "6282334307984525233", "sequence_length": 906, "latency": 1.647036075592041, "success": true, "timestamp": 1748476335.765585, "batch_id": "batch_3", "batch_index": 2, "processing_mode": "sequential"}, {"request_id": "req_32", "context_hash": "-6118425561090981083", "sequence_length": 668, "latency": 1.6437492370605469, "success": true, "timestamp": 1748476337.4126508, "batch_id": "batch_3", "batch_index": 2, "processing_mode": "sequential"}, {"request_id": "req_35", "context_hash": "6499229912946433423", "sequence_length": 664, "latency": 1.645263910293579, "success": true, "timestamp": 1748476339.0564299, "batch_id": "batch_3", "batch_index": 2, "processing_mode": "sequential"}, {"request_id": "req_60", "context_hash": "7827176742408056147", "sequence_length": 909, "latency": 1.6485042572021484, "success": true, "timestamp": 1748476340.7019413, "batch_id": "batch_4", "batch_index": 3, "processing_mode": "sequential"}, {"request_id": "req_52", "context_hash": "7827176742408056147", "sequence_length": 909, "latency": 1.6489322185516357, "success": true, "timestamp": 1748476342.3504806, "batch_id": "batch_4", "batch_index": 3, "processing_mode": "sequential"}, {"request_id": "req_57", "context_hash": "7827176742408056147", "sequence_length": 911, "latency": 1.6468403339385986, "success": true, "timestamp": 1748476343.9994423, "batch_id": "batch_4", "batch_index": 3, "processing_mode": "sequential"}, {"request_id": "req_56", "context_hash": "6499229912946433423", "sequence_length": 663, "latency": 1.6431715488433838, "success": true, "timestamp": 1748476345.6463099, "batch_id": "batch_4", "batch_index": 3, "processing_mode": "sequential"}, {"request_id": "req_59", "context_hash": "6499229912946433423", "sequence_length": 664, "latency": 1.6389610767364502, "success": true, "timestamp": 1748476347.2895095, "batch_id": "batch_4", "batch_index": 3, "processing_mode": "sequential"}, {"request_id": "req_47", "context_hash": "2982775370186856643", "sequence_length": 808, "latency": 1.6452183723449707, "success": true, "timestamp": 1748476348.9284973, "batch_id": "batch_4", "batch_index": 3, "processing_mode": "sequential"}, {"request_id": "req_58", "context_hash": "2982775370186856643", "sequence_length": 809, "latency": 1.6440119743347168, "success": true, "timestamp": 1748476350.5737383, "batch_id": "batch_4", "batch_index": 3, "processing_mode": "sequential"}, {"request_id": "req_53", "context_hash": "5381588843390861417", "sequence_length": 20998, "latency": 2.311913013458252, "success": true, "timestamp": 1748476352.2177722, "batch_id": "batch_4", "batch_index": 3, "processing_mode": "sequential"}, {"request_id": "req_50", "context_hash": "-2056895311811022652", "sequence_length": 610, "latency": 1.6405375003814697, "success": true, "timestamp": 1748476354.5297174, "batch_id": "batch_4", "batch_index": 3, "processing_mode": "sequential"}, {"request_id": "req_51", "context_hash": "6282334307984525233", "sequence_length": 904, "latency": 1.648280143737793, "success": true, "timestamp": 1748476356.1702828, "batch_id": "batch_4", "batch_index": 3, "processing_mode": "sequential"}, {"request_id": "req_48", "context_hash": "7913960499510928454", "sequence_length": 680, "latency": 1.6457278728485107, "success": true, "timestamp": 1748476357.818587, "batch_id": "batch_4", "batch_index": 3, "processing_mode": "sequential"}, {"request_id": "req_55", "context_hash": "-6118425561090981083", "sequence_length": 668, "latency": 1.6434342861175537, "success": true, "timestamp": 1748476359.4643395, "batch_id": "batch_4", "batch_index": 3, "processing_mode": "sequential"}, {"request_id": "req_49", "context_hash": "7899067936298576244", "sequence_length": 764, "latency": 1.6449944972991943, "success": true, "timestamp": 1748476361.1077964, "batch_id": "batch_4", "batch_index": 3, "processing_mode": "sequential"}, {"request_id": "req_54", "context_hash": "3427642709178807400", "sequence_length": 894, "latency": 1.6483590602874756, "success": true, "timestamp": 1748476362.7528095, "batch_id": "batch_4", "batch_index": 3, "processing_mode": "sequential"}, {"request_id": "req_46", "context_hash": "-9049639821994633092", "sequence_length": 703, "latency": 1.644310712814331, "success": true, "timestamp": 1748476364.4011896, "batch_id": "batch_4", "batch_index": 3, "processing_mode": "sequential"}, {"request_id": "req_61", "context_hash": "6282334307984525233", "sequence_length": 905, "latency": 1.6474900245666504, "success": true, "timestamp": 1748476366.0457687, "batch_id": "batch_5", "batch_index": 4, "processing_mode": "sequential"}, {"request_id": "req_74", "context_hash": "6282334307984525233", "sequence_length": 907, "latency": 1.6475274562835693, "success": true, "timestamp": 1748476367.6932864, "batch_id": "batch_5", "batch_index": 4, "processing_mode": "sequential"}, {"request_id": "req_65", "context_hash": "990700289451623570", "sequence_length": 791, "latency": 1.6466012001037598, "success": true, "timestamp": 1748476369.3408396, "batch_id": "batch_5", "batch_index": 4, "processing_mode": "sequential"}, {"request_id": "req_73", "context_hash": "990700289451623570", "sequence_length": 793, "latency": 1.6442131996154785, "success": true, "timestamp": 1748476370.9874623, "batch_id": "batch_5", "batch_index": 4, "processing_mode": "sequential"}, {"request_id": "req_75", "context_hash": "2982775370186856643", "sequence_length": 808, "latency": 1.6426630020141602, "success": true, "timestamp": 1748476372.6316967, "batch_id": "batch_5", "batch_index": 4, "processing_mode": "sequential"}, {"request_id": "req_64", "context_hash": "2982775370186856643", "sequence_length": 809, "latency": 1.6459853649139404, "success": true, "timestamp": 1748476374.2743807, "batch_id": "batch_5", "batch_index": 4, "processing_mode": "sequential"}, {"request_id": "req_69", "context_hash": "-6118425561090981083", "sequence_length": 666, "latency": 1.643134355545044, "success": true, "timestamp": 1748476375.9203863, "batch_id": "batch_5", "batch_index": 4, "processing_mode": "sequential"}, {"request_id": "req_72", "context_hash": "-6118425561090981083", "sequence_length": 666, "latency": 0.09015750885009766, "success": true, "timestamp": 1748476377.5635424, "batch_id": "batch_5", "batch_index": 4, "processing_mode": "sequential"}, {"request_id": "req_68", "context_hash": "5381588843390861417", "sequence_length": 21001, "latency": 0.8728747367858887, "success": true, "timestamp": 1748476377.653722, "batch_id": "batch_5", "batch_index": 4, "processing_mode": "sequential"}, {"request_id": "req_66", "context_hash": "7899067936298576244", "sequence_length": 766, "latency": 1.6386327743530273, "success": true, "timestamp": 1748476378.5266283, "batch_id": "batch_5", "batch_index": 4, "processing_mode": "sequential"}, {"request_id": "req_67", "context_hash": "-2056895311811022652", "sequence_length": 610, "latency": 1.6424014568328857, "success": true, "timestamp": 1748476380.165287, "batch_id": "batch_5", "batch_index": 4, "processing_mode": "sequential"}, {"request_id": "req_71", "context_hash": "7827176742408056147", "sequence_length": 909, "latency": 1.6460933685302734, "success": true, "timestamp": 1748476381.8077111, "batch_id": "batch_5", "batch_index": 4, "processing_mode": "sequential"}, {"request_id": "req_63", "context_hash": "6499229912946433423", "sequence_length": 666, "latency": 1.6423618793487549, "success": true, "timestamp": 1748476383.453831, "batch_id": "batch_5", "batch_index": 4, "processing_mode": "sequential"}, {"request_id": "req_70", "context_hash": "-9049639821994633092", "sequence_length": 703, "latency": 1.6440372467041016, "success": true, "timestamp": 1748476385.0962183, "batch_id": "batch_5", "batch_index": 4, "processing_mode": "sequential"}, {"request_id": "req_62", "context_hash": "-432210505875366949", "sequence_length": 925, "latency": 1.6460959911346436, "success": true, "timestamp": 1748476386.7402937, "batch_id": "batch_5", "batch_index": 4, "processing_mode": "sequential"}]}
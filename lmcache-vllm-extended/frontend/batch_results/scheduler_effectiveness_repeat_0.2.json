{"experiment_config": {"num_batches": 5, "batch_size": 10, "repeat_ratio": 0.2, "diversity_level": 1.0, "processing_mode": "sequential", "max_concurrent": null}, "metrics": {"total_requests": 50, "successful_requests": 50, "failed_requests": 0, "average_latency": 0.4787195444107056, "total_latency": 23.93597722053528, "throughput": 2.0889057312898736}, "detailed_results": [{"request_id": "req_5", "context_hash": "2445887265072817240", "sequence_length": 665, "latency": 1.4006342887878418, "success": true, "timestamp": 1748475966.1278958, "batch_id": "batch_1", "batch_index": 0, "processing_mode": "sequential"}, {"request_id": "req_6", "context_hash": "401736281489790958", "sequence_length": 679, "latency": 0.43384361267089844, "success": true, "timestamp": 1748475967.5285847, "batch_id": "batch_1", "batch_index": 0, "processing_mode": "sequential"}, {"request_id": "req_1", "context_hash": "6179760436773423026", "sequence_length": 792, "latency": 0.4005868434906006, "success": true, "timestamp": 1748475967.962472, "batch_id": "batch_1", "batch_index": 0, "processing_mode": "sequential"}, {"request_id": "req_8", "context_hash": "6665872659141338786", "sequence_length": 832, "latency": 0.9855320453643799, "success": true, "timestamp": 1748475968.363089, "batch_id": "batch_1", "batch_index": 0, "processing_mode": "sequential"}, {"request_id": "req_7", "context_hash": "5219296427256970827", "sequence_length": 885, "latency": 0.04591941833496094, "success": true, "timestamp": 1748475969.3486476, "batch_id": "batch_1", "batch_index": 0, "processing_mode": "sequential"}, {"request_id": "req_2", "context_hash": "8781486695688511147", "sequence_length": 905, "latency": 0.04302668571472168, "success": true, "timestamp": 1748475969.3945916, "batch_id": "batch_1", "batch_index": 0, "processing_mode": "sequential"}, {"request_id": "req_9", "context_hash": "8781486695688511147", "sequence_length": 905, "latency": 0.042333126068115234, "success": true, "timestamp": 1748475969.4376392, "batch_id": "batch_1", "batch_index": 0, "processing_mode": "sequential"}, {"request_id": "req_10", "context_hash": "8781486695688511147", "sequence_length": 907, "latency": 0.4013965129852295, "success": true, "timestamp": 1748475969.4799912, "batch_id": "batch_1", "batch_index": 0, "processing_mode": "sequential"}, {"request_id": "req_4", "context_hash": "-4618692910832722635", "sequence_length": 909, "latency": 0.04555368423461914, "success": true, "timestamp": 1748475969.881406, "batch_id": "batch_1", "batch_index": 0, "processing_mode": "sequential"}, {"request_id": "req_3", "context_hash": "6414839882846471730", "sequence_length": 924, "latency": 0.13920116424560547, "success": true, "timestamp": 1748475969.9269855, "batch_id": "batch_1", "batch_index": 0, "processing_mode": "sequential"}, {"request_id": "req_14", "context_hash": "-4618692910832722635", "sequence_length": 912, "latency": 0.4409925937652588, "success": true, "timestamp": 1748475970.0667217, "batch_id": "batch_2", "batch_index": 1, "processing_mode": "sequential"}, {"request_id": "req_17", "context_hash": "2445887265072817240", "sequence_length": 666, "latency": 1.4053351879119873, "success": true, "timestamp": 1748475970.5077484, "batch_id": "batch_2", "batch_index": 1, "processing_mode": "sequential"}, {"request_id": "req_16", "context_hash": "401736281489790958", "sequence_length": 680, "latency": 1.134946584701538, "success": true, "timestamp": 1748475971.91312, "batch_id": "batch_2", "batch_index": 1, "processing_mode": "sequential"}, {"request_id": "req_11", "context_hash": "-6831880410086486894", "sequence_length": 667, "latency": 1.4389476776123047, "success": true, "timestamp": 1748475973.0480978, "batch_id": "batch_2", "batch_index": 1, "processing_mode": "sequential"}, {"request_id": "req_19", "context_hash": "-615526752700839209", "sequence_length": 764, "latency": 0.6124188899993896, "success": true, "timestamp": 1748475974.4870794, "batch_id": "batch_2", "batch_index": 1, "processing_mode": "sequential"}, {"request_id": "req_12", "context_hash": "-615526752700839209", "sequence_length": 766, "latency": 1.657416820526123, "success": true, "timestamp": 1748475975.0995238, "batch_id": "batch_2", "batch_index": 1, "processing_mode": "sequential"}, {"request_id": "req_18", "context_hash": "-8437115704883647089", "sequence_length": 810, "latency": 1.3896434307098389, "success": true, "timestamp": 1748475976.7569666, "batch_id": "batch_2", "batch_index": 1, "processing_mode": "sequential"}, {"request_id": "req_13", "context_hash": "5406846816290299730", "sequence_length": 894, "latency": 0.04479527473449707, "success": true, "timestamp": 1748475978.1466336, "batch_id": "batch_2", "batch_index": 1, "processing_mode": "sequential"}, {"request_id": "req_15", "context_hash": "6938395699623608404", "sequence_length": 20998, "latency": 2.0650453567504883, "success": true, "timestamp": 1748475978.1914523, "batch_id": "batch_2", "batch_index": 1, "processing_mode": "sequential"}, {"request_id": "req_20", "context_hash": "6938395699623608404", "sequence_length": 21000, "latency": 0.32028937339782715, "success": true, "timestamp": 1748475980.2565305, "batch_id": "batch_2", "batch_index": 1, "processing_mode": "sequential"}, {"request_id": "req_21", "context_hash": "5406846816290299730", "sequence_length": 891, "latency": 0.1391594409942627, "success": true, "timestamp": 1748475980.5771766, "batch_id": "batch_3", "batch_index": 2, "processing_mode": "sequential"}, {"request_id": "req_29", "context_hash": "5406846816290299730", "sequence_length": 891, "latency": 0.07616996765136719, "success": true, "timestamp": 1748475980.716369, "batch_id": "batch_3", "batch_index": 2, "processing_mode": "sequential"}, {"request_id": "req_22", "context_hash": "8781486695688511147", "sequence_length": 904, "latency": 0.14061665534973145, "success": true, "timestamp": 1748475980.792559, "batch_id": "batch_3", "batch_index": 2, "processing_mode": "sequential"}, {"request_id": "req_30", "context_hash": "8781486695688511147", "sequence_length": 906, "latency": 0.07563972473144531, "success": true, "timestamp": 1748475980.933205, "batch_id": "batch_3", "batch_index": 2, "processing_mode": "sequential"}, {"request_id": "req_25", "context_hash": "-615526752700839209", "sequence_length": 764, "latency": 0.33478617668151855, "success": true, "timestamp": 1748475981.008867, "batch_id": "batch_3", "batch_index": 2, "processing_mode": "sequential"}, {"request_id": "req_28", "context_hash": "2445887265072817240", "sequence_length": 663, "latency": 0.07449507713317871, "success": true, "timestamp": 1748475981.3436732, "batch_id": "batch_3", "batch_index": 2, "processing_mode": "sequential"}, {"request_id": "req_23", "context_hash": "6665872659141338786", "sequence_length": 831, "latency": 0.8082797527313232, "success": true, "timestamp": 1748475981.4181895, "batch_id": "batch_3", "batch_index": 2, "processing_mode": "sequential"}, {"request_id": "req_26", "context_hash": "-4618692910832722635", "sequence_length": 910, "latency": 0.13195228576660156, "success": true, "timestamp": 1748475982.2264917, "batch_id": "batch_3", "batch_index": 2, "processing_mode": "sequential"}, {"request_id": "req_27", "context_hash": "6938395699623608404", "sequence_length": 21001, "latency": 1.5756275653839111, "success": true, "timestamp": 1748475982.358466, "batch_id": "batch_3", "batch_index": 2, "processing_mode": "sequential"}, {"request_id": "req_24", "context_hash": "-7196408731447545485", "sequence_length": 702, "latency": 0.8679637908935547, "success": true, "timestamp": 1748475983.9341235, "batch_id": "batch_3", "batch_index": 2, "processing_mode": "sequential"}, {"request_id": "req_36", "context_hash": "-4618692910832722635", "sequence_length": 909, "latency": 0.04517674446105957, "success": true, "timestamp": 1748475984.802318, "batch_id": "batch_4", "batch_index": 3, "processing_mode": "sequential"}, {"request_id": "req_40", "context_hash": "-4618692910832722635", "sequence_length": 909, "latency": 0.04317808151245117, "success": true, "timestamp": 1748475984.8475218, "batch_id": "batch_4", "batch_index": 3, "processing_mode": "sequential"}, {"request_id": "req_39", "context_hash": "6179760436773423026", "sequence_length": 790, "latency": 0.17656850814819336, "success": true, "timestamp": 1748475984.8907218, "batch_id": "batch_4", "batch_index": 3, "processing_mode": "sequential"}, {"request_id": "req_31", "context_hash": "6179760436773423026", "sequence_length": 793, "latency": 0.05868721008300781, "success": true, "timestamp": 1748475985.0673103, "batch_id": "batch_4", "batch_index": 3, "processing_mode": "sequential"}, {"request_id": "req_32", "context_hash": "5219296427256970827", "sequence_length": 882, "latency": 0.18683218955993652, "success": true, "timestamp": 1748475985.1260157, "batch_id": "batch_4", "batch_index": 3, "processing_mode": "sequential"}, {"request_id": "req_38", "context_hash": "-7196408731447545485", "sequence_length": 703, "latency": 0.7403397560119629, "success": true, "timestamp": 1748475985.3128679, "batch_id": "batch_4", "batch_index": 3, "processing_mode": "sequential"}, {"request_id": "req_35", "context_hash": "-6831880410086486894", "sequence_length": 666, "latency": 0.3680541515350342, "success": true, "timestamp": 1748475986.0532281, "batch_id": "batch_4", "batch_index": 3, "processing_mode": "sequential"}, {"request_id": "req_37", "context_hash": "8781486695688511147", "sequence_length": 904, "latency": 0.044054508209228516, "success": true, "timestamp": 1748475986.421303, "batch_id": "batch_4", "batch_index": 3, "processing_mode": "sequential"}, {"request_id": "req_33", "context_hash": "-8437115704883647089", "sequence_length": 808, "latency": 0.11284804344177246, "success": true, "timestamp": 1748475986.4653761, "batch_id": "batch_4", "batch_index": 3, "processing_mode": "sequential"}, {"request_id": "req_34", "context_hash": "-615526752700839209", "sequence_length": 766, "latency": 0.4992809295654297, "success": true, "timestamp": 1748475986.578242, "batch_id": "batch_4", "batch_index": 3, "processing_mode": "sequential"}, {"request_id": "req_48", "context_hash": "401736281489790958", "sequence_length": 681, "latency": 0.21403813362121582, "success": true, "timestamp": 1748475987.077741, "batch_id": "batch_5", "batch_index": 4, "processing_mode": "sequential"}, {"request_id": "req_49", "context_hash": "401736281489790958", "sequence_length": 682, "latency": 0.13312125205993652, "success": true, "timestamp": 1748475987.2918031, "batch_id": "batch_5", "batch_index": 4, "processing_mode": "sequential"}, {"request_id": "req_50", "context_hash": "6179760436773423026", "sequence_length": 792, "latency": 0.058643341064453125, "success": true, "timestamp": 1748475987.4249465, "batch_id": "batch_5", "batch_index": 4, "processing_mode": "sequential"}, {"request_id": "req_42", "context_hash": "6179760436773423026", "sequence_length": 792, "latency": 0.07472681999206543, "success": true, "timestamp": 1748475987.4836082, "batch_id": "batch_5", "batch_index": 4, "processing_mode": "sequential"}, {"request_id": "req_41", "context_hash": "6938395699623608404", "sequence_length": 21001, "latency": 0.896592378616333, "success": true, "timestamp": 1748475987.5583522, "batch_id": "batch_5", "batch_index": 4, "processing_mode": "sequential"}, {"request_id": "req_46", "context_hash": "-615526752700839209", "sequence_length": 763, "latency": 0.5601620674133301, "success": true, "timestamp": 1748475988.4549735, "batch_id": "batch_5", "batch_index": 4, "processing_mode": "sequential"}, {"request_id": "req_45", "context_hash": "5406846816290299730", "sequence_length": 894, "latency": 0.04407978057861328, "success": true, "timestamp": 1748475989.015159, "batch_id": "batch_5", "batch_index": 4, "processing_mode": "sequential"}, {"request_id": "req_43", "context_hash": "-8437115704883647089", "sequence_length": 807, "latency": 0.11245346069335938, "success": true, "timestamp": 1748475989.0592644, "batch_id": "batch_5", "batch_index": 4, "processing_mode": "sequential"}, {"request_id": "req_44", "context_hash": "6665872659141338786", "sequence_length": 829, "latency": 0.1158909797668457, "success": true, "timestamp": 1748475989.1717405, "batch_id": "batch_5", "batch_index": 4, "processing_mode": "sequential"}, {"request_id": "req_47", "context_hash": "6414839882846471730", "sequence_length": 922, "latency": 0.7786998748779297, "success": true, "timestamp": 1748475989.287653, "batch_id": "batch_5", "batch_index": 4, "processing_mode": "sequential"}]}
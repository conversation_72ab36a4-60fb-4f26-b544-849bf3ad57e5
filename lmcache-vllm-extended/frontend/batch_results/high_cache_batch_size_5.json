{"experiment_config": {"num_batches": 5, "batch_size": 5, "repeat_ratio": 0.7, "diversity_level": 0.3, "processing_mode": "parallel", "max_concurrent": 10}, "metrics": {"total_requests": 25, "successful_requests": 25, "failed_requests": 0, "average_latency": 1.7934568881988526, "total_latency": 44.83642220497131, "throughput": 0.5575824022200434}, "detailed_results": [{"request_id": "req_2", "context_hash": "-6931981366333838412", "sequence_length": 663, "latency": 0.7405426502227783, "success": true, "timestamp": 1748476083.4245102, "batch_id": "batch_1", "batch_index": 0, "processing_mode": "parallel"}, {"request_id": "req_5", "context_hash": "-6931981366333838412", "sequence_length": 664, "latency": 1.8305883407592773, "success": true, "timestamp": 1748476083.4250548, "batch_id": "batch_1", "batch_index": 0, "processing_mode": "parallel"}, {"request_id": "req_3", "context_hash": "-6931981366333838412", "sequence_length": 666, "latency": 1.830552339553833, "success": true, "timestamp": 1748476083.425192, "batch_id": "batch_1", "batch_index": 0, "processing_mode": "parallel"}, {"request_id": "req_1", "context_hash": "2612908187581994755", "sequence_length": 904, "latency": 1.8305222988128662, "success": true, "timestamp": 1748476083.4252605, "batch_id": "batch_1", "batch_index": 0, "processing_mode": "parallel"}, {"request_id": "req_4", "context_hash": "2612908187581994755", "sequence_length": 907, "latency": 1.8304967880249023, "success": true, "timestamp": 1748476083.4253204, "batch_id": "batch_1", "batch_index": 0, "processing_mode": "parallel"}, {"request_id": "req_8", "context_hash": "2612908187581994755", "sequence_length": 906, "latency": 1.7392005920410156, "success": true, "timestamp": 1748476085.2562425, "batch_id": "batch_2", "batch_index": 1, "processing_mode": "parallel"}, {"request_id": "req_10", "context_hash": "2612908187581994755", "sequence_length": 906, "latency": 0.17229032516479492, "success": true, "timestamp": 1748476085.2564042, "batch_id": "batch_2", "batch_index": 1, "processing_mode": "parallel"}, {"request_id": "req_7", "context_hash": "2612908187581994755", "sequence_length": 907, "latency": 1.7390861511230469, "success": true, "timestamp": 1748476085.2564733, "batch_id": "batch_2", "batch_index": 1, "processing_mode": "parallel"}, {"request_id": "req_9", "context_hash": "2612908187581994755", "sequence_length": 907, "latency": 1.7389910221099854, "success": true, "timestamp": 1748476085.2565308, "batch_id": "batch_2", "batch_index": 1, "processing_mode": "parallel"}, {"request_id": "req_6", "context_hash": "-6527821362746433580", "sequence_length": 883, "latency": 0.10369658470153809, "success": true, "timestamp": 1748476085.2565818, "batch_id": "batch_2", "batch_index": 1, "processing_mode": "parallel"}, {"request_id": "req_11", "context_hash": "-5476656492575015374", "sequence_length": 666, "latency": 1.899003505706787, "success": true, "timestamp": 1748476086.995925, "batch_id": "batch_3", "batch_index": 2, "processing_mode": "parallel"}, {"request_id": "req_14", "context_hash": "-5476656492575015374", "sequence_length": 669, "latency": 1.898646593093872, "success": true, "timestamp": 1748476086.9960709, "batch_id": "batch_3", "batch_index": 2, "processing_mode": "parallel"}, {"request_id": "req_12", "context_hash": "-5463293387632536532", "sequence_length": 910, "latency": 1.8987572193145752, "success": true, "timestamp": 1748476086.9961374, "batch_id": "batch_3", "batch_index": 2, "processing_mode": "parallel"}, {"request_id": "req_15", "context_hash": "-5463293387632536532", "sequence_length": 912, "latency": 1.898667812347412, "success": true, "timestamp": 1748476086.9961946, "batch_id": "batch_3", "batch_index": 2, "processing_mode": "parallel"}, {"request_id": "req_13", "context_hash": "-5463293387632536532", "sequence_length": 912, "latency": 1.8985795974731445, "success": true, "timestamp": 1748476086.996247, "batch_id": "batch_3", "batch_index": 2, "processing_mode": "parallel"}, {"request_id": "req_17", "context_hash": "-656321688808388582", "sequence_length": 764, "latency": 2.806225299835205, "success": true, "timestamp": 1748476088.8952637, "batch_id": "batch_4", "batch_index": 3, "processing_mode": "parallel"}, {"request_id": "req_18", "context_hash": "-656321688808388582", "sequence_length": 765, "latency": 2.8060410022735596, "success": true, "timestamp": 1748476088.8954053, "batch_id": "batch_4", "batch_index": 3, "processing_mode": "parallel"}, {"request_id": "req_19", "context_hash": "-656321688808388582", "sequence_length": 765, "latency": 2.5318849086761475, "success": true, "timestamp": 1748476088.895467, "batch_id": "batch_4", "batch_index": 3, "processing_mode": "parallel"}, {"request_id": "req_16", "context_hash": "-5710131233335491145", "sequence_length": 20998, "latency": 0.806053638458252, "success": true, "timestamp": 1748476088.89552, "batch_id": "batch_4", "batch_index": 3, "processing_mode": "parallel"}, {"request_id": "req_20", "context_hash": "-5710131233335491145", "sequence_length": 21001, "latency": 2.805535078048706, "success": true, "timestamp": 1748476088.895825, "batch_id": "batch_4", "batch_index": 3, "processing_mode": "parallel"}, {"request_id": "req_23", "context_hash": "-5463293387632536532", "sequence_length": 909, "latency": 2.006406784057617, "success": true, "timestamp": 1748476091.7018905, "batch_id": "batch_5", "batch_index": 4, "processing_mode": "parallel"}, {"request_id": "req_21", "context_hash": "-5463293387632536532", "sequence_length": 911, "latency": 2.0063014030456543, "success": true, "timestamp": 1748476091.7020721, "batch_id": "batch_5", "batch_index": 4, "processing_mode": "parallel"}, {"request_id": "req_25", "context_hash": "-5463293387632536532", "sequence_length": 911, "latency": 2.0062670707702637, "success": true, "timestamp": 1748476091.702139, "batch_id": "batch_5", "batch_index": 4, "processing_mode": "parallel"}, {"request_id": "req_22", "context_hash": "1916895176458092036", "sequence_length": 922, "latency": 2.0059926509857178, "success": true, "timestamp": 1748476091.7021928, "batch_id": "batch_5", "batch_index": 4, "processing_mode": "parallel"}, {"request_id": "req_24", "context_hash": "1916895176458092036", "sequence_length": 925, "latency": 2.0060925483703613, "success": true, "timestamp": 1748476091.702244, "batch_id": "batch_5", "batch_index": 4, "processing_mode": "parallel"}]}
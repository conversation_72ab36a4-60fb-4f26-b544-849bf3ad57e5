{"experiment_config": {"num_batches": 5, "batch_size": 10, "repeat_ratio": 0.7, "diversity_level": 0.3, "processing_mode": "parallel", "max_concurrent": 10}, "metrics": {"total_requests": 50, "successful_requests": 50, "failed_requests": 0, "average_latency": 2.0796393013000487, "total_latency": 103.98196506500244, "throughput": 0.4808526167854532}, "detailed_results": [{"request_id": "req_7", "context_hash": "-7369551672603320981", "sequence_length": 665, "latency": 2.1092281341552734, "success": true, "timestamp": 1748476098.1894288, "batch_id": "batch_1", "batch_index": 0, "processing_mode": "parallel"}, {"request_id": "req_2", "context_hash": "-7369551672603320981", "sequence_length": 666, "latency": 2.108543634414673, "success": true, "timestamp": 1748476098.1899426, "batch_id": "batch_1", "batch_index": 0, "processing_mode": "parallel"}, {"request_id": "req_5", "context_hash": "3052228197703872028", "sequence_length": 808, "latency": 2.108518123626709, "success": true, "timestamp": 1748476098.190069, "batch_id": "batch_1", "batch_index": 0, "processing_mode": "parallel"}, {"request_id": "req_3", "context_hash": "3052228197703872028", "sequence_length": 809, "latency": 2.1084177494049072, "success": true, "timestamp": 1748476098.190136, "batch_id": "batch_1", "batch_index": 0, "processing_mode": "parallel"}, {"request_id": "req_10", "context_hash": "3052228197703872028", "sequence_length": 810, "latency": 2.10842227935791, "success": true, "timestamp": 1748476098.1902003, "batch_id": "batch_1", "batch_index": 0, "processing_mode": "parallel"}, {"request_id": "req_1", "context_hash": "-3998749538968825984", "sequence_length": 924, "latency": 2.1081016063690186, "success": true, "timestamp": 1748476098.1902547, "batch_id": "batch_1", "batch_index": 0, "processing_mode": "parallel"}, {"request_id": "req_9", "context_hash": "-3998749538968825984", "sequence_length": 924, "latency": 2.108213424682617, "success": true, "timestamp": 1748476098.190308, "batch_id": "batch_1", "batch_index": 0, "processing_mode": "parallel"}, {"request_id": "req_6", "context_hash": "-3998749538968825984", "sequence_length": 924, "latency": 2.1077980995178223, "success": true, "timestamp": 1748476098.190355, "batch_id": "batch_1", "batch_index": 0, "processing_mode": "parallel"}, {"request_id": "req_8", "context_hash": "-3998749538968825984", "sequence_length": 925, "latency": 2.1080000400543213, "success": true, "timestamp": 1748476098.1904016, "batch_id": "batch_1", "batch_index": 0, "processing_mode": "parallel"}, {"request_id": "req_4", "context_hash": "-3998749538968825984", "sequence_length": 925, "latency": 2.1078684329986572, "success": true, "timestamp": 1748476098.190572, "batch_id": "batch_1", "batch_index": 0, "processing_mode": "parallel"}, {"request_id": "req_12", "context_hash": "-7369551672603320981", "sequence_length": 665, "latency": 2.053392171859741, "success": true, "timestamp": 1748476100.2992005, "batch_id": "batch_2", "batch_index": 1, "processing_mode": "parallel"}, {"request_id": "req_17", "context_hash": "-7369551672603320981", "sequence_length": 666, "latency": 2.053673028945923, "success": true, "timestamp": 1748476100.299383, "batch_id": "batch_2", "batch_index": 1, "processing_mode": "parallel"}, {"request_id": "req_13", "context_hash": "-6373556485808371162", "sequence_length": 790, "latency": 2.0534121990203857, "success": true, "timestamp": 1748476100.2994528, "batch_id": "batch_2", "batch_index": 1, "processing_mode": "parallel"}, {"request_id": "req_20", "context_hash": "-6373556485808371162", "sequence_length": 791, "latency": 2.053579330444336, "success": true, "timestamp": 1748476100.2995126, "batch_id": "batch_2", "batch_index": 1, "processing_mode": "parallel"}, {"request_id": "req_15", "context_hash": "-6373556485808371162", "sequence_length": 793, "latency": 2.0532596111297607, "success": true, "timestamp": 1748476100.299564, "batch_id": "batch_2", "batch_index": 1, "processing_mode": "parallel"}, {"request_id": "req_19", "context_hash": "-2755314502896581299", "sequence_length": 909, "latency": 2.053325653076172, "success": true, "timestamp": 1748476100.2996173, "batch_id": "batch_2", "batch_index": 1, "processing_mode": "parallel"}, {"request_id": "req_11", "context_hash": "-2755314502896581299", "sequence_length": 909, "latency": 2.0533504486083984, "success": true, "timestamp": 1748476100.2996676, "batch_id": "batch_2", "batch_index": 1, "processing_mode": "parallel"}, {"request_id": "req_14", "context_hash": "-2755314502896581299", "sequence_length": 910, "latency": 2.053060531616211, "success": true, "timestamp": 1748476100.2997155, "batch_id": "batch_2", "batch_index": 1, "processing_mode": "parallel"}, {"request_id": "req_18", "context_hash": "-2755314502896581299", "sequence_length": 911, "latency": 2.053220510482788, "success": true, "timestamp": 1748476100.2997613, "batch_id": "batch_2", "batch_index": 1, "processing_mode": "parallel"}, {"request_id": "req_16", "context_hash": "-2755314502896581299", "sequence_length": 912, "latency": 2.0530993938446045, "success": true, "timestamp": 1748476100.2998068, "batch_id": "batch_2", "batch_index": 1, "processing_mode": "parallel"}, {"request_id": "req_28", "context_hash": "-3998749538968825984", "sequence_length": 923, "latency": 2.1561012268066406, "success": true, "timestamp": 1748476102.353613, "batch_id": "batch_3", "batch_index": 2, "processing_mode": "parallel"}, {"request_id": "req_22", "context_hash": "-3998749538968825984", "sequence_length": 923, "latency": 2.155832529067993, "success": true, "timestamp": 1748476102.3538043, "batch_id": "batch_3", "batch_index": 2, "processing_mode": "parallel"}, {"request_id": "req_30", "context_hash": "-3998749538968825984", "sequence_length": 924, "latency": 2.1558077335357666, "success": true, "timestamp": 1748476102.3538702, "batch_id": "batch_3", "batch_index": 2, "processing_mode": "parallel"}, {"request_id": "req_25", "context_hash": "-3998749538968825984", "sequence_length": 925, "latency": 2.155965805053711, "success": true, "timestamp": 1748476102.3539271, "batch_id": "batch_3", "batch_index": 2, "processing_mode": "parallel"}, {"request_id": "req_29", "context_hash": "-3998749538968825984", "sequence_length": 925, "latency": 2.155439615249634, "success": true, "timestamp": 1748476102.3539784, "batch_id": "batch_3", "batch_index": 2, "processing_mode": "parallel"}, {"request_id": "req_26", "context_hash": "3052228197703872028", "sequence_length": 808, "latency": 2.155791997909546, "success": true, "timestamp": 1748476102.3540294, "batch_id": "batch_3", "batch_index": 2, "processing_mode": "parallel"}, {"request_id": "req_23", "context_hash": "3052228197703872028", "sequence_length": 808, "latency": 2.1557064056396484, "success": true, "timestamp": 1748476102.35408, "batch_id": "batch_3", "batch_index": 2, "processing_mode": "parallel"}, {"request_id": "req_24", "context_hash": "3052228197703872028", "sequence_length": 809, "latency": 2.155458688735962, "success": true, "timestamp": 1748476102.3541343, "batch_id": "batch_3", "batch_index": 2, "processing_mode": "parallel"}, {"request_id": "req_27", "context_hash": "3052228197703872028", "sequence_length": 810, "latency": 2.155564785003662, "success": true, "timestamp": 1748476102.3541837, "batch_id": "batch_3", "batch_index": 2, "processing_mode": "parallel"}, {"request_id": "req_21", "context_hash": "-6975050597987972879", "sequence_length": 763, "latency": 2.155625820159912, "success": true, "timestamp": 1748476102.3542314, "batch_id": "batch_3", "batch_index": 2, "processing_mode": "parallel"}, {"request_id": "req_32", "context_hash": "-7369551672603320981", "sequence_length": 663, "latency": 2.0553722381591797, "success": true, "timestamp": 1748476104.5103588, "batch_id": "batch_4", "batch_index": 3, "processing_mode": "parallel"}, {"request_id": "req_40", "context_hash": "-7369551672603320981", "sequence_length": 664, "latency": 2.0549309253692627, "success": true, "timestamp": 1748476104.5105247, "batch_id": "batch_4", "batch_index": 3, "processing_mode": "parallel"}, {"request_id": "req_38", "context_hash": "-7369551672603320981", "sequence_length": 664, "latency": 2.0550789833068848, "success": true, "timestamp": 1748476104.510588, "batch_id": "batch_4", "batch_index": 3, "processing_mode": "parallel"}, {"request_id": "req_37", "context_hash": "-7369551672603320981", "sequence_length": 666, "latency": 2.0548572540283203, "success": true, "timestamp": 1748476104.5106416, "batch_id": "batch_4", "batch_index": 3, "processing_mode": "parallel"}, {"request_id": "req_39", "context_hash": "-2755314502896581299", "sequence_length": 912, "latency": 2.0549068450927734, "success": true, "timestamp": 1748476104.5106916, "batch_id": "batch_4", "batch_index": 3, "processing_mode": "parallel"}, {"request_id": "req_31", "context_hash": "-2755314502896581299", "sequence_length": 912, "latency": 2.0548861026763916, "success": true, "timestamp": 1748476104.5107446, "batch_id": "batch_4", "batch_index": 3, "processing_mode": "parallel"}, {"request_id": "req_35", "context_hash": "-8196431394742080051", "sequence_length": 891, "latency": 2.0549018383026123, "success": true, "timestamp": 1748476104.5107968, "batch_id": "batch_4", "batch_index": 3, "processing_mode": "parallel"}, {"request_id": "req_36", "context_hash": "-8196431394742080051", "sequence_length": 893, "latency": 2.054443597793579, "success": true, "timestamp": 1748476104.5108464, "batch_id": "batch_4", "batch_index": 3, "processing_mode": "parallel"}, {"request_id": "req_33", "context_hash": "-8196431394742080051", "sequence_length": 893, "latency": 2.0546727180480957, "success": true, "timestamp": 1748476104.5108936, "batch_id": "batch_4", "batch_index": 3, "processing_mode": "parallel"}, {"request_id": "req_34", "context_hash": "-8196431394742080051", "sequence_length": 894, "latency": 2.0545918941497803, "success": true, "timestamp": 1748476104.5109408, "batch_id": "batch_4", "batch_index": 3, "processing_mode": "parallel"}, {"request_id": "req_42", "context_hash": "-6373556485808371162", "sequence_length": 791, "latency": 2.026463508605957, "success": true, "timestamp": 1748476106.5662029, "batch_id": "batch_5", "batch_index": 4, "processing_mode": "parallel"}, {"request_id": "req_47", "context_hash": "-6373556485808371162", "sequence_length": 793, "latency": 2.026099443435669, "success": true, "timestamp": 1748476106.5663736, "batch_id": "batch_5", "batch_index": 4, "processing_mode": "parallel"}, {"request_id": "req_44", "context_hash": "8743727711948499117", "sequence_length": 679, "latency": 2.0259504318237305, "success": true, "timestamp": 1748476106.5664399, "batch_id": "batch_5", "batch_index": 4, "processing_mode": "parallel"}, {"request_id": "req_46", "context_hash": "8743727711948499117", "sequence_length": 679, "latency": 2.0260791778564453, "success": true, "timestamp": 1748476106.5664923, "batch_id": "batch_5", "batch_index": 4, "processing_mode": "parallel"}, {"request_id": "req_45", "context_hash": "8743727711948499117", "sequence_length": 680, "latency": 2.0259640216827393, "success": true, "timestamp": 1748476106.566543, "batch_id": "batch_5", "batch_index": 4, "processing_mode": "parallel"}, {"request_id": "req_43", "context_hash": "8743727711948499117", "sequence_length": 682, "latency": 2.025947332382202, "success": true, "timestamp": 1748476106.566592, "batch_id": "batch_5", "batch_index": 4, "processing_mode": "parallel"}, {"request_id": "req_48", "context_hash": "-5148011810263170947", "sequence_length": 701, "latency": 2.0255820751190186, "success": true, "timestamp": 1748476106.56664, "batch_id": "batch_5", "batch_index": 4, "processing_mode": "parallel"}, {"request_id": "req_49", "context_hash": "-5148011810263170947", "sequence_length": 703, "latency": 2.025743246078491, "success": true, "timestamp": 1748476106.566691, "batch_id": "batch_5", "batch_index": 4, "processing_mode": "parallel"}, {"request_id": "req_41", "context_hash": "-5148011810263170947", "sequence_length": 703, "latency": 2.025862216949463, "success": true, "timestamp": 1748476106.566738, "batch_id": "batch_5", "batch_index": 4, "processing_mode": "parallel"}, {"request_id": "req_50", "context_hash": "-5148011810263170947", "sequence_length": 703, "latency": 2.0258522033691406, "success": true, "timestamp": 1748476106.566782, "batch_id": "batch_5", "batch_index": 4, "processing_mode": "parallel"}]}
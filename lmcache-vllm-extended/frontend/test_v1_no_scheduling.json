{"experiment_config": {"num_batches": 2, "batch_size": 5, "repeat_ratio": 0.3, "diversity_level": 1.0, "processing_mode": "sequential", "max_concurrent": null, "api_version": "v1", "scheduling": "client-side"}, "metrics": {"total_requests": 10, "successful_requests": 10, "failed_requests": 0, "average_latency": 1.792369532585144, "total_latency": 17.92369532585144, "throughput": 0.557920664137654}, "detailed_results": [{"request_id": "req_1", "context_hash": "4994438875425405066", "sequence_length": 461, "latency": 2.2765846252441406, "success": true, "timestamp": 1748592755.5646763, "batch_id": "batch_55564", "batch_index": 0, "processing_mode": "sequential"}, {"request_id": "req_2", "context_hash": "4994438875425405066", "sequence_length": 458, "latency": 1.7601463794708252, "success": true, "timestamp": 1748592757.841302, "batch_id": "batch_55564", "batch_index": 0, "processing_mode": "sequential"}, {"request_id": "req_3", "context_hash": "-5200719083536782155", "sequence_length": 468, "latency": 1.8369121551513672, "success": true, "timestamp": 1748592759.6014829, "batch_id": "batch_55564", "batch_index": 0, "processing_mode": "sequential"}, {"request_id": "req_4", "context_hash": "-2459630029753732964", "sequence_length": 451, "latency": 1.9646048545837402, "success": true, "timestamp": 1748592761.4384272, "batch_id": "batch_55564", "batch_index": 0, "processing_mode": "sequential"}, {"request_id": "req_5", "context_hash": "22843439886978778", "sequence_length": 536, "latency": 0.7461814880371094, "success": true, "timestamp": 1748592763.4030633, "batch_id": "batch_55564", "batch_index": 0, "processing_mode": "sequential"}, {"request_id": "req_1", "context_hash": "829407455190956185", "sequence_length": 507, "latency": 1.8790390491485596, "success": true, "timestamp": 1748592764.1494682, "batch_id": "batch_55564", "batch_index": 1, "processing_mode": "sequential"}, {"request_id": "req_2", "context_hash": "7732951010675371070", "sequence_length": 486, "latency": 2.0021724700927734, "success": true, "timestamp": 1748592766.028537, "batch_id": "batch_55564", "batch_index": 1, "processing_mode": "sequential"}, {"request_id": "req_3", "context_hash": "829407455190956185", "sequence_length": 507, "latency": 1.7640326023101807, "success": true, "timestamp": 1748592768.0307434, "batch_id": "batch_55564", "batch_index": 1, "processing_mode": "sequential"}, {"request_id": "req_4", "context_hash": "22843439886978778", "sequence_length": 533, "latency": 1.8488085269927979, "success": true, "timestamp": 1748592769.794806, "batch_id": "batch_55564", "batch_index": 1, "processing_mode": "sequential"}, {"request_id": "req_5", "context_hash": "-7190547408435812088", "sequence_length": 458, "latency": 1.8452131748199463, "success": true, "timestamp": 1748592771.6436565, "batch_id": "batch_55564", "batch_index": 1, "processing_mode": "sequential"}]}
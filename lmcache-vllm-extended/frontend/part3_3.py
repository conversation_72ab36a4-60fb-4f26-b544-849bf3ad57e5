import time
import os
import chat_session
from typing import Dict
from transformers import AutoTokenizer

# Change the following variables as needed
MODEL_NAME = "Qwen/Qwen2.5-1.5B-Instruct"
IP1 = "************"
PORT1 = 8000

def get_tokenizer():
    global MODEL_NAME
    tokenizer = AutoTokenizer.from_pretrained(MODEL_NAME)
    return tokenizer

tokenizer = get_tokenizer()

def read_chunks(file_folder) -> Dict[str, str]:
    """
    Read all the txt files in the folder and return the filenames
    """
    filenames = os.listdir(file_folder)
    ret = {}
    for filename in filenames:
        if not filename.endswith("txt"):
            continue
        key = filename.removesuffix(".txt")
        with open(os.path.join(file_folder, filename), "r") as fin:
            value = fin.read()
        ret[key] = value

    return ret

searched_chunk_name = ""

with open("top_chunk.txt", "r") as f:
    searched_chunk_name = f.read().strip()


chunks = read_chunks("data/")
system_prompt = " You are a helpful assistant. I will now give you a document and please answer my question afterwards based on the content in document. "
prompt = ""

with open("user_query.txt", "r") as f:
    prompt = f.read().strip()

session = chat_session.ChatSession(IP1,PORT1)


selected_chunks = [searched_chunk_name]  # Use the searched chunk name
contexts = [chunks[key] for key in selected_chunks]

session.set_context([system_prompt] + contexts)

num_tokens = tokenizer.encode(session.get_context())

for chunk in session.chat(prompt):
    time.sleep(0.05)
# 批处理调度器快速开始指南

## 🚀 快速开始

### 1. 安装依赖
```bash
pip install aiohttp pandas matplotlib seaborn numpy
```

### 2. 启动LLM服务器
```bash
# 在项目根目录
cd lmcache-vllm-extended
LMCACHE_CONFIG_FILE=configuration.yaml python3 lmcache_vllm/script.py serve <model_name> --port 8000
```

### 3. 运行批处理测试
```bash
# 在frontend目录
cd frontend

# 运行单个批处理测试
python3 batch_request_generator.py --ip localhost --port 8000 --num_batches 3 --batch_size 5 --output test_result.json

# 运行完整实验套件
python3 run_batch_experiments.py --ip localhost --port 8000

# 分析结果

```python3 batch_analyzer.py --input_files batch_results/*.json --output_dir analysis/

## 🔧 故障排除

### 问题1: "Command 'python' not found"
**解决方案**: 使用 `python3` 而不是 `python`

### 问题2: "None of PyTorch, TensorFlow >= 2.0, or Flax have been found"
**解决方案**: 这是警告，不影响功能。tokenizer仍然可以正常工作。

### 问题3: 所有请求失败 (HTTP 404)
**解决方案**: 确保LLM服务器正在运行并监听正确的端口

### 问题4: "No successful requests found"
**解决方案**: 这表示服务器没有运行或无法连接。检查服务器状态。

## 📊 理解结果

### 批处理生成器输出
- `experiment_config`: 实验配置参数
- `metrics`: 整体性能指标
- `detailed_results`: 每个请求的详细结果

### 调度器效果
- 请求按上下文哈希分组
- 已缓存的上下文优先处理
- 组内按序列长度排序

### 关键指标
- **延迟 (Latency)**: 单个请求的响应时间
- **吞吐量 (Throughput)**: 每秒处理的请求数
- **上下文重用率**: 相同上下文的请求比例
- **成功率**: 成功处理的请求比例

## 🎯 实验设计

### 实验1: 调度器效果
测试不同重复比例下的性能改善

### 实验2: 缓存大小影响
测试不同批处理大小在高缓存环境下的表现

### 实验3: 多样性影响
测试请求多样性对性能的影响

### 实验4: 上下文大小影响
测试不同上下文大小的处理效果

### 实验5: 处理模式比较
比较顺序处理和并行处理的性能

## 📈 分析图表

生成的图表包括：
- `context_reuse_vs_latency.png`: 上下文重用率与延迟关系
- `batch_size_analysis.png`: 批处理大小分析
- `diversity_analysis.png`: 多样性影响分析
- `sequence_length_analysis.png`: 序列长度影响分析
- `processing_mode_comparison.png`: 处理模式比较

## 🔍 深入分析

### 调度器策略
1. **分组策略**: 按上下文哈希分组相似请求
2. **优先级策略**: 已缓存上下文优先处理
3. **排序策略**: 组内按序列长度排序
4. **负载均衡**: 支持并行处理模式

### 性能优化建议
1. 使用中等批处理大小 (10-20个请求)
2. 尽可能分组相似上下文的请求
3. 考虑序列长度分布进行批处理
4. 监控缓存命中率优化调度器性能

## 📝 示例命令

```bash
# 测试不同批处理大小
python3 batch_request_generator.py --ip localhost --port 8000 --batch_size 5 --output small_batch.json
python3 batch_request_generator.py --ip localhost --port 8000 --batch_size 20 --output large_batch.json

# 测试不同重复比例
python3 batch_request_generator.py --ip localhost --port 8000 --repeat_ratio 0.1 --output low_repeat.json
python3 batch_request_generator.py --ip localhost --port 8000 --repeat_ratio 0.7 --output high_repeat.json

# 测试不同多样性级别
python3 batch_request_generator.py --ip localhost --port 8000 --diversity 0.3 --output low_diversity.json
python3 batch_request_generator.py --ip localhost --port 8000 --diversity 1.0 --output high_diversity.json

# 比较处理模式
python3 batch_request_generator.py --ip localhost --port 8000 --processing_mode sequential --output sequential.json
python3 batch_request_generator.py --ip localhost --port 8000 --processing_mode parallel --max_concurrent 5 --output parallel.json
```

## 🎓 研究问题

这个系统回答以下研究问题：

1. **调度器是否改善了性能指标？为什么？**
2. **如果本地缓存有很大的大小会发生什么？**
3. **批处理大小如何影响调度器的效果？**
4. **当请求多样性增加时会发生什么？**
5. **当请求有更大的上下文大小时会发生什么？**

通过运行实验和分析结果，你可以获得这些问题的详细答案。

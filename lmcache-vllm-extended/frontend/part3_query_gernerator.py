# this is the query generator for the task3
# generate the 1 querys based on content of the document

import time
import os
import chat_session
from typing import Dict
from transformers import AutoTokenizer
import pandas as pd

# Change the following variables as needed
MODEL_NAME = "Qwen/Qwen2.5-1.5B-Instruct"
IP1 = "************"
PORT1 = 8000

def get_tokenizer():
    global MODEL_NAME
    tokenizer = AutoTokenizer.from_pretrained(MODEL_NAME)
    return tokenizer

tokenizer = get_tokenizer()

def read_chunks(file_folder) -> Dict[str, str]:
    """
    Read all the txt files in the folder and return the filenames
    """
    filenames = os.listdir(file_folder)
    ret = {}
    for filename in filenames:
        if not filename.endswith("txt"):
            continue
        key = filename.removesuffix(".txt")
        with open(os.path.join(file_folder, filename), "r") as fin:
            value = fin.read()
        ret[key] = value

    return ret

chunks = read_chunks("data/")
system_prompt = " You are a helpful assistant. I will now give you a document and please answer my question afterwards based on the content in document. "
prompt = " Please straightly generate 1 query based on the content of the document below. The query should be a question that can be answered by the content. "

session = chat_session.ChatSession(IP1,PORT1)

gererated_queries = {}
for var in chunks:
    selected_chunks = [var] 
    contexts = [chunks[key] for key in selected_chunks]
    session.messages = []  # 清空上次的上下文
    session.set_context([system_prompt] + contexts)
    num_tokens = tokenizer.encode(session.get_context())

    response = ""
    for chunk in session.chat(prompt):
        response += chunk
        time.sleep(0.05)    
    gererated_queries[var] = response.strip()

df = pd.DataFrame.from_dict(gererated_queries, orient='index')
df.to_parquet("gererated_queries.parquet")
import time
import os
import chat_session
from typing import Dict
from transformers import AutoTokenizer

def get_response() -> str:
    """
    Get the response from the chat session based on the prompt and context.
    """
    # Change the following variables as needed
    MODEL_NAME = "Qwen/Qwen2.5-1.5B-Instruct"
    IP1 = "************"
    PORT1 = 8000

    def get_tokenizer():
        tokenizer = AutoTokenizer.from_pretrained(MODEL_NAME)
        return tokenizer

    tokenizer = get_tokenizer()

    def read_chunk(file_folder, file_name) -> Dict[str, str]:
        """
        Read all the txt files in the folder and return the filenames
        """

        ret = {}
        key = file_name
        file_name_with_suffix = file_name + ".txt"
        with open(os.path.join(file_folder, file_name_with_suffix), "r") as fin:
            value = fin.read()
        ret[key] = value
        return ret

    searched_chunk_name = ""

    with open("top_chunk.txt", "r") as f:
        searched_chunk_name = f.read().strip()


    chunks = read_chunk("data/", searched_chunk_name)
    system_prompt = " You are a helpful assistant. I will now give you a document and please answer my question afterwards based on the content in document. "
    prompt = ""

    with open("user_query.txt", "r") as f:
        prompt = f.read().strip()

    session = chat_session.ChatSession(IP1,PORT1)


    selected_chunks = [searched_chunk_name]  # Use the searched chunk name
    contexts = [chunks[key] for key in selected_chunks]

    session.set_context([system_prompt] + contexts)

    num_tokens = tokenizer.encode(session.get_context())
    response = ""

    for chunk in session.chat(prompt):        
        response += chunk  
    return response.strip()

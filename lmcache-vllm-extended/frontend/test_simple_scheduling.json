{"experiment_config": {"num_batches": 3, "batch_size": 10, "repeat_ratio": 0.4, "diversity_level": 1.0, "processing_mode": "sequential", "max_concurrent": null, "api_version": "v1", "scheduling": "client-side"}, "metrics": {"total_requests": 30, "successful_requests": 30, "failed_requests": 0, "average_latency": 2.0719128847122192, "total_latency": 62.15738654136658, "throughput": 0.4826457750123486}, "detailed_results": [{"request_id": "req_1", "context_hash": "-6286321045856254504", "sequence_length": 537, "latency": 2.9550654888153076, "success": true, "timestamp": 1748594223.898489, "batch_id": "batch_23896", "batch_index": 0, "processing_mode": "sequential"}, {"request_id": "req_2", "context_hash": "-4144602170624788022", "sequence_length": 451, "latency": 1.7980358600616455, "success": true, "timestamp": 1748594226.8535943, "batch_id": "batch_23896", "batch_index": 0, "processing_mode": "sequential"}, {"request_id": "req_3", "context_hash": "-1606188518783429314", "sequence_length": 12827, "latency": 8.849438190460205, "success": true, "timestamp": 1748594228.6516654, "batch_id": "batch_23896", "batch_index": 0, "processing_mode": "sequential"}, {"request_id": "req_4", "context_hash": "-307065366577467008", "sequence_length": 507, "latency": 2.5787482261657715, "success": true, "timestamp": 1748594237.5011399, "batch_id": "batch_23896", "batch_index": 0, "processing_mode": "sequential"}, {"request_id": "req_5", "context_hash": "-4144602170624788022", "sequence_length": 451, "latency": 3.2242867946624756, "success": true, "timestamp": 1748594240.07992, "batch_id": "batch_23896", "batch_index": 0, "processing_mode": "sequential"}, {"request_id": "req_6", "context_hash": "-6286321045856254504", "sequence_length": 540, "latency": 0.21871137619018555, "success": true, "timestamp": 1748594243.3042395, "batch_id": "batch_23896", "batch_index": 0, "processing_mode": "sequential"}, {"request_id": "req_7", "context_hash": "1577976851885768555", "sequence_length": 545, "latency": 1.8348112106323242, "success": true, "timestamp": 1748594243.5229828, "batch_id": "batch_23896", "batch_index": 0, "processing_mode": "sequential"}, {"request_id": "req_8", "context_hash": "-4144602170624788022", "sequence_length": 449, "latency": 1.7318873405456543, "success": true, "timestamp": 1748594245.3578262, "batch_id": "batch_23896", "batch_index": 0, "processing_mode": "sequential"}, {"request_id": "req_9", "context_hash": "-1606188518783429314", "sequence_length": 12830, "latency": 3.1888010501861572, "success": true, "timestamp": 1748594247.0897446, "batch_id": "batch_23896", "batch_index": 0, "processing_mode": "sequential"}, {"request_id": "req_10", "context_hash": "8862104210903095179", "sequence_length": 482, "latency": 1.824359655380249, "success": true, "timestamp": 1748594250.2785795, "batch_id": "batch_23896", "batch_index": 0, "processing_mode": "sequential"}, {"request_id": "req_1", "context_hash": "7678270231914336745", "sequence_length": 492, "latency": 1.7226755619049072, "success": true, "timestamp": 1748594252.1031587, "batch_id": "batch_23897", "batch_index": 1, "processing_mode": "sequential"}, {"request_id": "req_2", "context_hash": "-5077658498153475185", "sequence_length": 512, "latency": 1.838935136795044, "success": true, "timestamp": 1748594253.8258646, "batch_id": "batch_23897", "batch_index": 1, "processing_mode": "sequential"}, {"request_id": "req_3", "context_hash": "-3328778997321676812", "sequence_length": 506, "latency": 0.6754434108734131, "success": true, "timestamp": 1748594255.6648304, "batch_id": "batch_23897", "batch_index": 1, "processing_mode": "sequential"}, {"request_id": "req_4", "context_hash": "-5321862415991769019", "sequence_length": 459, "latency": 1.8046634197235107, "success": true, "timestamp": 1748594256.340302, "batch_id": "batch_23897", "batch_index": 1, "processing_mode": "sequential"}, {"request_id": "req_5", "context_hash": "-1654960849309176745", "sequence_length": 555, "latency": 1.8411922454833984, "success": true, "timestamp": 1748594258.1449957, "batch_id": "batch_23897", "batch_index": 1, "processing_mode": "sequential"}, {"request_id": "req_6", "context_hash": "-3328778997321676812", "sequence_length": 503, "latency": 1.7429108619689941, "success": true, "timestamp": 1748594259.98622, "batch_id": "batch_23897", "batch_index": 1, "processing_mode": "sequential"}, {"request_id": "req_7", "context_hash": "-1654960849309176745", "sequence_length": 553, "latency": 1.7186248302459717, "success": true, "timestamp": 1748594261.7291622, "batch_id": "batch_23897", "batch_index": 1, "processing_mode": "sequential"}, {"request_id": "req_8", "context_hash": "-6286321045856254504", "sequence_length": 540, "latency": 1.6748480796813965, "success": true, "timestamp": 1748594263.4478161, "batch_id": "batch_23897", "batch_index": 1, "processing_mode": "sequential"}, {"request_id": "req_9", "context_hash": "-5321862415991769019", "sequence_length": 458, "latency": 1.742307424545288, "success": true, "timestamp": 1748594265.1226945, "batch_id": "batch_23897", "batch_index": 1, "processing_mode": "sequential"}, {"request_id": "req_10", "context_hash": "-6286321045856254504", "sequence_length": 539, "latency": 1.7366292476654053, "success": true, "timestamp": 1748594266.865032, "batch_id": "batch_23897", "batch_index": 1, "processing_mode": "sequential"}, {"request_id": "req_1", "context_hash": "8862104210903095179", "sequence_length": 479, "latency": 1.8362751007080078, "success": true, "timestamp": 1748594268.6018531, "batch_id": "batch_23897", "batch_index": 2, "processing_mode": "sequential"}, {"request_id": "req_2", "context_hash": "5978150336440110967", "sequence_length": 468, "latency": 1.808980941772461, "success": true, "timestamp": 1748594270.4381568, "batch_id": "batch_23897", "batch_index": 2, "processing_mode": "sequential"}, {"request_id": "req_3", "context_hash": "-3509698031470932030", "sequence_length": 458, "latency": 1.799062728881836, "success": true, "timestamp": 1748594272.2471688, "batch_id": "batch_23897", "batch_index": 2, "processing_mode": "sequential"}, {"request_id": "req_4", "context_hash": "8862104210903095179", "sequence_length": 480, "latency": 1.7692735195159912, "success": true, "timestamp": 1748594274.0462642, "batch_id": "batch_23897", "batch_index": 2, "processing_mode": "sequential"}, {"request_id": "req_5", "context_hash": "-5077658498153475185", "sequence_length": 514, "latency": 1.7418692111968994, "success": true, "timestamp": 1748594275.8155713, "batch_id": "batch_23897", "batch_index": 2, "processing_mode": "sequential"}, {"request_id": "req_6", "context_hash": "3259654666609832381", "sequence_length": 483, "latency": 1.5345752239227295, "success": true, "timestamp": 1748594277.5574687, "batch_id": "batch_23897", "batch_index": 2, "processing_mode": "sequential"}, {"request_id": "req_7", "context_hash": "-3509698031470932030", "sequence_length": 458, "latency": 1.7295045852661133, "success": true, "timestamp": 1748594279.0920699, "batch_id": "batch_23897", "batch_index": 2, "processing_mode": "sequential"}, {"request_id": "req_8", "context_hash": "3259654666609832381", "sequence_length": 486, "latency": 1.7447514533996582, "success": true, "timestamp": 1748594280.821607, "batch_id": "batch_23897", "batch_index": 2, "processing_mode": "sequential"}, {"request_id": "req_9", "context_hash": "-307065366577467008", "sequence_length": 507, "latency": 1.7428135871887207, "success": true, "timestamp": 1748594282.566387, "batch_id": "batch_23897", "batch_index": 2, "processing_mode": "sequential"}, {"request_id": "req_10", "context_hash": "-307065366577467008", "sequence_length": 508, "latency": 1.7479047775268555, "success": true, "timestamp": 1748594284.309228, "batch_id": "batch_23897", "batch_index": 2, "processing_mode": "sequential"}]}
#!/usr/bin/env python3
"""
Run comprehensive batch processing experiments to analyze scheduler performance.

This script runs multiple experiments with different configurations to answer
the research questions about batch processing and scheduling effectiveness.
"""

import os
import subprocess
import time
import argparse
from datetime import datetime
from typing import List, Dict, Any


class BatchExperimentRunner:
    """Runs a series of batch processing experiments."""

    def __init__(self, server_ip: str, server_port: int, output_dir: str = "batch_results"):
        self.server_ip = server_ip
        self.server_port = server_port
        self.output_dir = output_dir
        self.experiment_counter = 0

        # Create output directory
        os.makedirs(output_dir, exist_ok=True)

    def run_single_experiment(
        self,
        num_batches: int,
        batch_size: int,
        repeat_ratio: float,
        diversity: float,
        processing_mode: str = "sequential",
        max_concurrent: int = 5,
        experiment_name: str = None
    ) -> str:
        """Run a single batch processing experiment."""

        self.experiment_counter += 1

        if experiment_name is None:
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            experiment_name = f"exp_{self.experiment_counter}_{timestamp}"

        output_file = os.path.join(self.output_dir, f"{experiment_name}.json")

        # Build command
        cmd = [
            "python3", "batch_request_generator.py",
            "--ip", self.server_ip,
            "--port", str(self.server_port),
            "--num_batches", str(num_batches),
            "--batch_size", str(batch_size),
            "--repeat_ratio", str(repeat_ratio),
            "--diversity", str(diversity),
            "--processing_mode", processing_mode,
            "--max_concurrent", str(max_concurrent),
            "--output", output_file
        ]

        print(f"Running experiment: {experiment_name}")
        print(f"  Batches: {num_batches}, Batch Size: {batch_size}")
        print(f"  Repeat Ratio: {repeat_ratio}, Diversity: {diversity}")
        print(f"  Processing Mode: {processing_mode}")

        try:
            subprocess.run(cmd, check=True)
            print(f"  ✓ Completed: {output_file}")
            return output_file
        except subprocess.CalledProcessError as e:
            print(f"  ✗ Failed: {e}")
            return ""

    def experiment_1_scheduler_effectiveness(self) -> List[str]:

        print("\n=== Experiment 1: Scheduler Effectiveness ===")

        output_files = []
        repeat_ratios = [0.0, 0.2, 0.4, 0.6, 0.8]  
        for repeat_ratio in repeat_ratios:
            output_file = self.run_single_experiment(
                num_batches=5,
                batch_size=10,
                repeat_ratio=repeat_ratio,
                diversity=1.0,  # Full diversity to see scheduler impact
                experiment_name=f"scheduler_effectiveness_repeat_{repeat_ratio}"
            )
            if output_file:
                output_files.append(output_file)
            time.sleep(2)  # Small delay between experiments

        return output_files

    def experiment_2_cache_size_impact(self) -> List[str]:
    
        print("\n=== Experiment 2: Cache Size Impact ===")

        output_files = []
        batch_sizes = [5, 10, 20, 30]  # Different batch sizes

        # Test with high context reuse (simulating high cache hit rate)
        for batch_size in batch_sizes:
            output_file = self.run_single_experiment(
                num_batches=5,
                batch_size=batch_size,
                repeat_ratio=0.7,         
                diversity=0.3,            
                processing_mode="parallel",   
                max_concurrent=10,            
                experiment_name=f"high_cache_batch_size_{batch_size}"
            )
            if output_file:
                output_files.append(output_file)
            time.sleep(2)

        return output_files

    def experiment_3_diversity_impact(self) -> List[str]:
       
        print("\n=== Experiment 3: Diversity Impact ===")

        output_files = []
        diversity_levels = [0.2, 0.4, 0.6, 0.8, 1.0]  # From low to high diversity

        for diversity in diversity_levels:
            output_file = self.run_single_experiment(
                num_batches=5,
                batch_size=15,
                repeat_ratio=0.3,  
                diversity=diversity,
                experiment_name=f"diversity_impact_{diversity}"
            )
            if output_file:
                output_files.append(output_file)
            time.sleep(2)

        return output_files

    def experiment_4_context_size_impact(self) -> List[str]:
        """
        Experiment 4: Test impact of larger context sizes.
        Question: What happens when requests have larger context sizes?

        Note: This experiment uses different diversity levels as a proxy for
        context size variation, since larger diversity means more unique contexts.
        """
        print("\n=== Experiment 4: Context Size Impact ===")

        output_files = []

        # Test with different combinations that affect context processing
        configs = [
            {"diversity": 0.3, "batch_size": 10, "name": "small_contexts"},
            {"diversity": 0.6, "batch_size": 10, "name": "medium_contexts"},
            {"diversity": 1.0, "batch_size": 10, "name": "large_contexts"},
            {"diversity": 1.0, "batch_size": 20, "name": "large_contexts_big_batch"}
        ]

        for config in configs:
            output_file = self.run_single_experiment(
                num_batches=5,
                batch_size=config["batch_size"],
                repeat_ratio=0.2,  # Low repeat ratio to test scheduler with diverse contexts
                diversity=config["diversity"],
                experiment_name=f"context_size_{config['name']}"
            )
            if output_file:
                output_files.append(output_file)
            time.sleep(2)

        return output_files

    def experiment_5_processing_mode_comparison(self) -> List[str]:
        print("\n=== Experiment 5: Processing Mode Comparison ===")

        output_files = []

        # Test both processing modes with same parameters
        configs = [
            {"mode": "sequential", "concurrent": 1},
            {"mode": "parallel", "concurrent": 3},
            {"mode": "parallel", "concurrent": 5},
            {"mode": "parallel", "concurrent": 10}
        ]

        for config in configs:
            output_file = self.run_single_experiment(
                num_batches=5,
                batch_size=15,
                repeat_ratio=0.4,
                diversity=0.7,
                processing_mode=config["mode"],
                max_concurrent=config["concurrent"],
                experiment_name=f"processing_{config['mode']}_concurrent_{config['concurrent']}"
            )
            if output_file:
                output_files.append(output_file)
            time.sleep(2)

        return output_files

    def run_all_experiments(self) -> List[str]:
        """Run all experiments and return list of output files."""
        all_output_files = []

        # Run all experiment sets
        all_output_files.extend(self.experiment_1_scheduler_effectiveness())
        all_output_files.extend(self.experiment_2_cache_size_impact())
        all_output_files.extend(self.experiment_3_diversity_impact())
        all_output_files.extend(self.experiment_4_context_size_impact())
        all_output_files.extend(self.experiment_5_processing_mode_comparison())

        return [f for f in all_output_files if f]  # Filter out empty strings

    def analyze_results(self, output_files: List[str], analysis_dir: str = "batch_analysis"):
        """Run analysis on all experiment results."""
        if not output_files:
            print("No output files to analyze")
            return

        print(f"\n=== Analyzing {len(output_files)} experiment results ===")

        cmd = [
            "python3", "batch_analyzer.py",
            "--input_files"
        ] + output_files + [
            "--output_dir", analysis_dir
        ]

        try:
            subprocess.run(cmd, check=True)
            print(f"Analysis completed. Results saved to {analysis_dir}/")
        except subprocess.CalledProcessError as e:
            print(f"Analysis failed: {e}")


def main():
    parser = argparse.ArgumentParser(description='Run comprehensive batch processing experiments')

    # Server connection
    parser.add_argument("--ip", required=True, help="Server IP address")
    parser.add_argument("--port", type=int, required=True, help="Server port")

    # Experiment configuration
    parser.add_argument("--output_dir", default="batch_results", help="Directory for experiment results")
    parser.add_argument("--analysis_dir", default="batch_analysis", help="Directory for analysis results")
    parser.add_argument("--experiments", nargs="+",
                        choices=["scheduler", "cache", "diversity", "context", "processing", "all"],
                        default=["all"], help="Which experiments to run")

    args = parser.parse_args()

    # Initialize experiment runner
    runner = BatchExperimentRunner(args.ip, args.port, args.output_dir)

    # Run selected experiments
    all_output_files = []

    if "all" in args.experiments:
        all_output_files = runner.run_all_experiments()
    else:
        if "scheduler" in args.experiments:
            all_output_files.extend(runner.experiment_1_scheduler_effectiveness())
        if "cache" in args.experiments:
            all_output_files.extend(runner.experiment_2_cache_size_impact())
        if "diversity" in args.experiments:
            all_output_files.extend(runner.experiment_3_diversity_impact())
        if "context" in args.experiments:
            all_output_files.extend(runner.experiment_4_context_size_impact())
        if "processing" in args.experiments:
            all_output_files.extend(runner.experiment_5_processing_mode_comparison())

    # Analyze results
    if all_output_files:
        runner.analyze_results(all_output_files, args.analysis_dir)

        print(f"\n=== Experiment Summary ===")
        print(f"Total experiments completed: {len(all_output_files)}")
        print(f"Results saved to: {args.output_dir}/")
        print(f"Analysis saved to: {args.analysis_dir}/")
        print(f"\nTo view the analysis report, check: {args.analysis_dir}/analysis_report.md")
    else:
        print("No experiments completed successfully")


if __name__ == "__main__":
    main()

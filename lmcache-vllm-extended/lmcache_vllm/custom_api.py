import vllm.entrypoints.openai.api_server as base_api
from vllm.entrypoints.openai.protocol import *
from fastapi import APIRouter, Request
import asyncio
import time
from collections import defaultdict
from typing import List, Dict, Any
import hashlib

# You should use the following file to implement all APIs you may require in the project.
# Note that the two important ones are already implemented here simply by calling the default v1 implementation in VLLM.
# You may need to modify these functions to enable pre-processing of requests, before running the inference.

extended_router = APIRouter()

class RequestScheduler:
    """Smart request scheduler that groups requests by context for cache optimization."""

    def __init__(self):
        self.processed_contexts = set()
        self.pending_requests = []
        self.batch_size = 10
        self.max_wait_time = 1.0  # Maximum time to wait for batching (seconds)
        self.last_batch_time = time.time()

    def get_context_hash(self, messages: List[Dict[str, str]]) -> str:
        """Generate hash for the context (system message)."""
        # Extract system message content
        system_content = ""
        for msg in messages:
            if msg.get("role") == "system":
                system_content = msg.get("content", "")
                break

        # Generate hash
        return str(hash(system_content))

    def should_process_batch(self) -> bool:
        """Determine if we should process the current batch."""
        if len(self.pending_requests) >= self.batch_size:
            return True

        if len(self.pending_requests) > 0:
            time_since_last = time.time() - self.last_batch_time
            if time_since_last >= self.max_wait_time:
                return True

        return False

    def schedule_requests(self, requests: List[Dict]) -> List[Dict]:
        """Schedule requests for optimal cache reuse."""
        if not requests:
            return []

        # Group requests by context hash
        context_groups = defaultdict(list)
        for req in requests:
            context_hash = req['context_hash']
            context_groups[context_hash].append(req)

        scheduled_requests = []

        # Debug output
        print(f"\n[LLM Scheduler] Processing {len(requests)} requests:")
        print(f"  → Unique contexts: {len(context_groups)}")
        for context_hash, group in context_groups.items():
            print(f"  → Context {context_hash[:8]}... → {len(group)} requests")

        # First, process requests with contexts that are already cached
        cached_groups = []
        new_groups = []

        for context_hash, group_requests in context_groups.items():
            if context_hash in self.processed_contexts:
                cached_groups.append((context_hash, group_requests))
            else:
                new_groups.append((context_hash, group_requests))

        # Sort cached groups by number of requests (larger groups first)
        cached_groups.sort(key=lambda x: len(x[1]), reverse=True)

        # Sort new groups by estimated sequence length
        new_groups.sort(key=lambda x: len(str(x[1][0].get('messages', []))))

        # Process cached groups first
        for context_hash, group_requests in cached_groups:
            scheduled_requests.extend(group_requests)

        # Then process new groups
        for context_hash, group_requests in new_groups:
            scheduled_requests.extend(group_requests)
            self.processed_contexts.add(context_hash)

        print(f"  → Scheduled order optimized for cache reuse")
        return scheduled_requests

# Global scheduler instance
scheduler = RequestScheduler()

@extended_router.get("/models")
async def show_available_models(request: Request):
    print("v2 models is called!")
    return await base_api.show_available_models(request)


@extended_router.post("/chat/completions")
async def create_chat_completion(request: ChatCompletionRequest,
                                 raw_request: Request):
    print("v2 completion is called with smart scheduling")

    # For now, let's implement a simple version that just logs the context hash
    # and processes the request normally
    context_hash = scheduler.get_context_hash(request.messages)
    print(f"Processing request with context hash: {context_hash[:8]}...")

    # Mark this context as processed for future cache optimization
    scheduler.processed_contexts.add(context_hash)

    # Process the request normally
    return await base_api.create_chat_completion(request, raw_request)